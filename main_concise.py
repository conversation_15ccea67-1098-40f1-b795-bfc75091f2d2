import os
import sys
import asyncio
import logging
import time
import yaml
from typing import List, Dict, Any
from pathlib import Path
from dotenv import load_dotenv
from livekit import rtc
from livekit.agents import cli, WorkerOptions, Agent, JobContext, AgentSession, metrics
from livekit.agents.voice.room_io import RoomInputOptions, RoomOutputOptions
from livekit.agents.llm import function_tool
from livekit.agents.voice import MetricsCollectedEvent
from livekit.agents.voice import RunContext
from livekit.plugins import noise_cancellation, google
from livekit.plugins.google.beta.realtime.realtime_api import RealtimeModel
from clients.vector_db_client import VectorDBClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Suppress verbose logs
for log_name in ['urllib3', 'requests', 'httpx', 'transformers', 'torch', 'chromadb']:
    logging.getLogger(log_name).setLevel(logging.WARNING)

# Load environment
load_dotenv()

# Constants
BASE_DIR = Path(__file__).resolve().parent
VECTOR_DB_PATH = BASE_DIR / "vectordb" / "bge-m3"
VECTOR_CONFIG_PATH = BASE_DIR / "vectordb" / "vectordb_config.yml"

class P2PLendingFunctions:
    """Simplified P2P lending functions"""
    
    def __init__(self, vector_db_client: VectorDBClient):
        self.vector_db_client = vector_db_client
        self.collection_name = 'faqs_p2p'
        
        # Load default max_results from config
        try:
            with open(VECTOR_CONFIG_PATH, 'r') as f:
                config = yaml.safe_load(f)
                self.default_max_results = config.get('retriever_defaults', {}).get('similarity', {}).get('k', 3)
        except Exception:
            self.default_max_results = 3
        
        # Initialize vector store
        try:
            self.vector_db_client.load_existing_collections([self.collection_name])
            logger.info("✅ Vector database loaded")
        except Exception as e:
            logger.error(f"❌ Vector database failed: {e}")

    def search_p2p_knowledge(self, user_query: str, max_results: int = None) -> List[Dict[str, Any]]:
        """Search P2P knowledge base"""
        try:
            if max_results is None:
                max_results = self.default_max_results
            
            documents = self.vector_db_client.query_vectordb(
                collection_name=self.collection_name,
                user_query=user_query,
                retriever_type="similarity"
            )
            
            results = []
            for doc in documents[:max_results]:
                results.append({
                    "content": doc.page_content,
                    "metadata": doc.metadata
                })
            
            logger.info(f"🔍 Vector search: {len(results)} results found")
            return results
            
        except Exception as e:
            logger.error(f"❌ Vector search error: {e}")
            return [{"content": "Search error occurred", "error": str(e)}]

class NeuraVoiceV2(Agent):
    """Simplified NeuraVoice Agent"""
    
    def __init__(self):
        # Lazy initialization - vector DB loaded only when needed
        self.vector_db_client = None
        self.p2p_functions = None
        self.token_count = 0
        
        # Validate environment
        self._validate_environment()
        
        # Simple, clear role instructions
        role_instructions = self._create_role_instructions()
        
        # Available voices
        selected_voice = 'Kore'
        
        super().__init__(
            instructions=role_instructions,
            llm=RealtimeModel(
                model='gemini-2.5-flash-exp-native-audio-thinking-dialog',
                voice=selected_voice,
                temperature=0.7,
                project=os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-project-459106'),
                location='us-central1'
            ),
            allow_interruptions=True
        )
        
        logger.info("✅ NeuraVoice V2 initialized")

    def _validate_environment(self):
        """Validate required environment variables"""
        if not os.getenv('GOOGLE_CLOUD_PROJECT'):
            os.environ['GOOGLE_CLOUD_PROJECT'] = 'ai-project-459106'
        
        required_vars = ['GOOGLE_APPLICATION_CREDENTIALS', 'GOOGLE_CLOUD_PROJECT']
        missing = [var for var in required_vars if not os.getenv(var)]
        if missing:
            raise ValueError(f"Missing environment variables: {missing}")

    def _create_role_instructions(self) -> str:
        """Clear, concise role instructions"""
        return """
You are Priya, a female P2P lending advisor at लेनदेन Club with 15 years of expertise.

PERSONALITY:
- Warm, empathetic, enthusiastic
- Explain complex concepts with simple analogies
- Keep responses short (1-2 sentences) and conversational
- Friendly companion who can discuss any topic

COMMUNICATION:
- Voice-only phone call interaction
- Use natural speech patterns with pauses and emphasis
- Write "लेनदेन Club" for LendenClub
- For numbers: "₹50,000" as "fifty thousand rupees"
- For percentages: "10-12%" as "ten to twelve percent"

CRITICAL FUNCTION CALLING RULE:
You MUST ALWAYS call search_knowledge_base() when user asks about:
- P2P lending
- लेनदेन Club 
- Finance topics
- RBI regulations
- Investments
- Company details

NEVER say "let me search" - just call the function and use results immediately.

When you get function results, respond naturally as if you knew the information directly.

EXAMPLE:
User: "What is P2P lending?"
[Function called automatically]
You: "P2P lending connects people who want to lend with those who need to borrow, cutting out banks..."

GUARDRAILS:
- Always remain respectful and professional
- Never repeat user's exact words
- If user says "linkedin club" or similar, treat as "लेनदेन Club"
- Keep promotional content subtle and natural

Your goal: Be a knowledgeable, friendly advisor who helps with P2P lending while engaging naturally on any topic.
"""

    def _ensure_kb_loaded(self):
        """Lazy load vector database only when needed"""
        if self.p2p_functions is None:
            logger.info("📚 Loading vector database...")
            self.vector_db_client = VectorDBClient(
                db_path=VECTOR_DB_PATH,
                config_path=VECTOR_CONFIG_PATH
            )
            self.p2p_functions = P2PLendingFunctions(self.vector_db_client)
            logger.info("✅ Knowledge base loaded")

    @function_tool
    async def search_knowledge_base(self, context: RunContext, user_query: str, max_results: int = 3):
        """
        Search P2P lending knowledge base.
        
        Args:
            context: RunContext from LiveKit
            user_query: User's question about P2P lending
            max_results: Maximum results to return
        """
        logger.info(f"🔍 KB Search: {user_query[:50]}...")
        
        # Ensure knowledge base is loaded
        self._ensure_kb_loaded()
        
        try:
            results = self.p2p_functions.search_p2p_knowledge(user_query, max_results)
            logger.info(f"✅ KB Search: {len(results)} results")
            
            if not results:
                return [{"content": "No specific information found. Could you rephrase?", "source": "fallback"}]
            
            formatted_results = []
            for i, result in enumerate(results, 1):
                formatted_results.append({
                    "content": result.get('content', ''),
                    "source": result.get('metadata', {}).get('source', 'Knowledge Base'),
                    "result_number": i
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"❌ KB search error: {e}")
            return [{"content": f"Search error occurred", "source": "error"}]

    async def on_enter(self):
        """Generate greeting when agent enters"""
        try:
            self.session.generate_reply(instructions="Greet the user briefly as Priya from लेनदेन Club")
            logger.info("✅ Greeting generated")
            await asyncio.sleep(2.0)  # Allow audio processing
        except Exception as e:
            logger.warning(f"⚠️ Greeting failed: {e}")

    def on_metrics_collected(self, event: MetricsCollectedEvent):
        """Track token usage and metrics"""
        for metric in event.metrics:
            if hasattr(metric, 'tokens_count') and metric.tokens_count:
                self.token_count += metric.tokens_count
                logger.info(f"🔢 Tokens used: {metric.tokens_count} (Total: {self.token_count})")

async def entrypoint(ctx: JobContext):
    """Main entrypoint"""
    logger.info("🚀 Starting NeuraVoice V2...")
    
    try:
        await ctx.connect()
        logger.info("✅ Connected to LiveKit")
        
        bot = NeuraVoiceV2()
        session = AgentSession()
        
        @session.on("metrics_collected")
        def _on_metrics_collected(event: MetricsCollectedEvent):
            metrics.log_metrics(event.metrics)
            bot.on_metrics_collected(event)

        await session.start(
            agent=bot,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                noise_cancellation=noise_cancellation.BVC()
            ),
            room_output_options=RoomOutputOptions(
                audio_enabled=True
            )
        )
        
        logger.info("✅ Session started successfully")
        
    except Exception as e:
        logger.exception(f"❌ Error: {e}")
        raise

if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))