def get_opener_prompt() -> str:
	"""
	Fetch opening line of conversational agent
	"""

	return """
	Generate one short, engaging, and friendly opening line for a {domain} assistant named {agent}.
	**Instructions**
	- The language should be {language}.
	- The tone should be warm, confident, and professional, making the user feel comfortable.
	- Keep the greeting natural and concise.

	**Output**
	Generate only the opening statement.
	"""



def get_core_user_prompt() -> str:
    """Enhanced user prompt for natural voice conversation"""

    return """
    Current user query: {user_query}
    Chat history: {chat_history}
    Available context: {context}
    NLU entities: {nlu_entities}
    User language: {user_language}
    Additional instructions: {instructions}
    Current time: {current_time_str}

    Generate a natural, conversational voice response that sounds like a knowledgeable financial advisor explaining concepts to a friend. Use appropriate cultural expressions, natural pauses, and speech patterns specific to {user_language}. 
    Vary your response style and approach based on previous interactions to maintain fresh, engaging conversation while preserving <PERSON><PERSON>'s warm, expert personality throughout the interaction.
    Execute micro-interaction protocols: Analyze user's conversational state, emotional undertones, and engagement level. Deploy appropriate turn-taking signals, backchanneling cues, prosodic modulation, pragmatic alignment, repair mechanisms, grounding techniques, and emotional calibration. 
    Respond with natural speech patterns that demonstrate active listening, maintain conversational flow, and build rapport through subtle linguistic mirroring and supportive feedback cues.

    """



def get_ooc_user_prompt() -> str:
    """Simplified user prompt for out-of-context queries - behavioral instructions now in system prompt"""
    return """
    Current user query: {user_query}
    Detected language: {user_language}
    Chat history: {chat_history}
    Current time: {current_time_str}

    Generate a natural, human-like spoken response to the user's out-of-context query. Use your general knowledge to provide helpful information while maintaining your warm, professional personality as Priya from लेनदेनClub. Be concise and direct, following the detailed response guidelines already established in your system instructions.
    Execute micro-interaction protocols: Analyze user's conversational state, emotional undertones, and engagement level. Deploy appropriate turn-taking signals, backchanneling cues, prosodic modulation, pragmatic alignment, repair mechanisms, grounding techniques, and emotional calibration. 
    Respond with natural speech patterns that demonstrate active listening, maintain conversational flow, and build rapport through subtle linguistic mirroring and supportive feedback cues.

    """
