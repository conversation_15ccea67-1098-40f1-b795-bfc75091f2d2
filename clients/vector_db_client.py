import os
import re
import itertools
import hashlib
import pickle
from typing import List, Dict, Any, Union, Optional
from collections import defaultdict
from functools import lru_cache
from langchain_community.vectorstores import Chroma
from langchain_community.embeddings import SentenceTransformerEmbeddings
from langchain.retrievers.ensemble import EnsembleRetriever
from langchain_core.documents.base import Document as LangchainDocument
import chromadb
import pandas as pd
import time
import yaml

class _CollectionManager:
    """
    Internal class to handle ChromaDB collections and retrieval.
    """
    def __init__(self, db_path: str, embedding_function):
        self._db_path = db_path
        self._embedding_function = embedding_function
        self._collections: Dict[str, Chroma] = {}

    def load_collection(self, collection_name: str) -> None:
        path = os.path.join(self._db_path, collection_name)
        self._collections[collection_name] = Chroma(
            embedding_function=self._embedding_function,
            persist_directory=path,
            collection_name=collection_name
        )

    def create_collection(self, collection_name: str, documents: List[LangchainDocument]) -> None:
        path = os.path.join(self._db_path, collection_name)
        vectordb = Chroma.from_documents(
            documents=documents,
            embedding=self._embedding_function,
            persist_directory=path,
            collection_name=collection_name
        )
        vectordb.persist()
        self._collections[collection_name] = vectordb

    def retrieve_similar_documents(
        self,
        collection_name: str,
        query: str,
        retriever_type: str,
        search_params: Dict
    ) -> List[LangchainDocument]:
        if collection_name not in self._collections:
            raise ValueError(f"Collection '{collection_name}' is not loaded.")
        retriever = self._collections[collection_name].as_retriever(
            search_type=retriever_type,
            search_kwargs=search_params
        )
        return retriever.get_relevant_documents(query)


class VectorDBClient:
    """
    High-level interface for building and querying vector DBs using Chroma and Langchain.
    Enhanced with persistent caching and local model support.
    """

    def __init__(
            self, 
            db_path: str = 'vectordb/chroma_db', 
            config_path: str = 'vectordb_config.yml', 
            pickle_path: str = None,
            cache_size: int = None,  # Will be overridden by config if present
            clear_cache_after: int = 1000  # Clear cache after this many queries
        ):
        self._db_path = db_path
        self._pickle_path = pickle_path
        self._config = self._load_config(config_path)
        
        # Set up local model directory if specified
        # model_kwargs = {"device": "cpu"}
        # if "local_model_dir" in self._config:
        #     local_model_dir = self._config["local_model_dir"]
        #     # Create directory if it doesn't exist
        #     os.makedirs(local_model_dir, exist_ok=True)
        #     model_kwargs["cache_folder"] = local_model_dir
        #     print(f"[INFO] Using local model directory: {local_model_dir}")
        
        self._embedding_function = SentenceTransformerEmbeddings(
            model_name=self._config["embedding_model"],
            model_kwargs={"device": "cpu"}
        )
        self._collection_manager = _CollectionManager(self._db_path, self._embedding_function)
        
        # Initialize cache with LRU eviction and persistence support
        self._cache = {}
        self._cache_order = []
        
        # Get cache configuration
        cache_config = self._config.get('cache', {})
        self._cache_size = cache_size or cache_config.get('size', 1000)
        self._clear_cache_after = clear_cache_after or cache_config.get('clear_after', 1000)
        self._persist_cache = cache_config.get('persist_cache', False)
        self._cache_file = cache_config.get('cache_file', 'vectordb/query_cache.pkl')
        
        # Query counter for cache clearing
        self._query_count = 0
        
        # Load persistent cache if enabled
        if self._persist_cache:
            self._load_persistent_cache()
        
    def _load_persistent_cache(self):
        """Load cache from disk if it exists."""
        try:
            if os.path.exists(self._cache_file):
                with open(self._cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    self._cache = cache_data.get('cache', {})
                    self._cache_order = cache_data.get('cache_order', [])
                    # Validate cache size
                    if len(self._cache) > self._cache_size:
                        # Trim cache to current size limit
                        excess = len(self._cache) - self._cache_size
                        for _ in range(excess):
                            oldest_key = self._cache_order.pop(0)
                            del self._cache[oldest_key]
                    print(f"[INFO] Loaded {len(self._cache)} cached queries from {self._cache_file}")
        except Exception as e:
            print(f"[WARNING] Could not load persistent cache: {e}")
            self._cache = {}
            self._cache_order = []
    
    def _save_persistent_cache(self):
        """Save cache to disk."""
        if not self._persist_cache:
            return
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self._cache_file), exist_ok=True)
            cache_data = {
                'cache': self._cache,
                'cache_order': self._cache_order,
                'timestamp': time.time()
            }
            with open(self._cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
            print(f"[INFO] Saved {len(self._cache)} cached queries to {self._cache_file}")
        except Exception as e:
            print(f"[WARNING] Could not save persistent cache: {e}")
        
    def _get_cache_key(self, collection_name: str, query: str, retriever_type: str) -> str:
        """Generate a consistent cache key for the query."""
        key_str = f"{collection_name}:{query}:{retriever_type}"
        return hashlib.md5(key_str.encode()).hexdigest()
        
    def _get_from_cache(self, key: str) -> Optional[List[LangchainDocument]]:
        """Retrieve item from cache if it exists."""
        if key in self._cache:
            # Move to end of order (most recently used)
            self._cache_order.remove(key)
            self._cache_order.append(key)
            return self._cache[key]
        return None
        
    def _set_to_cache(self, key: str, value: List[LangchainDocument]) -> None:
        """Add item to cache with LRU eviction policy."""
        if key in self._cache:
            self._cache_order.remove(key)
        elif len(self._cache) >= self._cache_size:
            # Remove least recently used item
            oldest_key = self._cache_order.pop(0)
            del self._cache[oldest_key]
            
        self._cache[key] = value
        self._cache_order.append(key)
        
        # Save to disk periodically (every 10 cache additions)
        if self._persist_cache and len(self._cache) % 10 == 0:
            self._save_persistent_cache()

    def load_knowledge_base_from_pickle(self, pickle_path: str) -> None:
        """
        Load a pickle file and create vector collections.
        """
        if not os.path.exists(pickle_path):
            raise FileNotFoundError(f"Knowledge base not found at: {pickle_path}")
        df = pd.read_pickle(pickle_path)
        print(df.shape)
        grouped = self._group_documents_by_domain(df)
        print('len grouped_df', len(grouped))
        for collection_name, docs in grouped.items():
            print(f"[INFO] Creating collection: {collection_name}")
            self._collection_manager.create_collection(collection_name, docs)

    def load_existing_collections(self, collection_names: List[str]) -> None:
        """
        Load existing Chroma collections from disk.
        """
        for name in collection_names:
            print(f"[INFO] Loading collection: {name}")
            self._collection_manager.load_collection(name)

    def query_vectordb(
        self,
        collection_name: str,
        user_query: str,
        retriever_type: str = "similarity"
    ) -> List[LangchainDocument]:
        """
        Retrieve documents similar to a user query using the configured retrieval method.
        Results are cached to improve performance for repeated queries.

        Args:
            collection_name (str): Name of the vector collection.
            user_query (str): The query string.
            retriever_type (str): One of 'similarity', 'mmr', or 'similarity_score_threshold'.

        Returns:
            List[LangchainDocument]: Retrieved and deduplicated documents.
        """
        # Check and clear cache if query threshold is reached
        self._query_count += 1
        if self._query_count >= self._clear_cache_after:
            self.clear_cache()
            self._query_count = 0
            print(f"Cache cleared after {self._clear_cache_after} queries")
        
        # Generate cache key
        cache_key = self._get_cache_key(collection_name, user_query, retriever_type)
        
        # Check cache first
        cached_result = self._get_from_cache(cache_key)
        if cached_result is not None:
            print(f'✅ Cache hit for query: {user_query[:50]}...')
            return cached_result
            
        print('⚡ Cache miss, querying vector DB...')
        if retriever_type not in self._config["retriever_defaults"]:
            raise ValueError(f"Unknown retriever_type: '{retriever_type}'")
        
        # print('coll name:', collection_name)
        # print('embedding model', self._embedding_function)
        # print('embedding path', self._db_path)
        
        search_params = self._config["retriever_defaults"][retriever_type]
        try:
            results = self._collection_manager.retrieve_similar_documents(
                collection_name=collection_name,
                query=user_query,
                retriever_type=retriever_type,
                search_params=search_params
            )
            # Deduplicate and cache the results
            deduped_results = self._deduplicate_documents(results)
            self._set_to_cache(cache_key, deduped_results)
            return deduped_results
            
        except Exception as error:
            print('Error in vector DB retrieval:')
            print(error)
            return []

    def extract_qna_pairs(self, documents: List[LangchainDocument]) -> Dict[str, List[str]]:
        """
        Extract and group unique Q&A pairs from retrieved documents.
        """
        qna_map = defaultdict(set)
        for doc in documents:
            match = re.match(r"(?i)Question:\s*(.+?)\s*Answer:\s*(.+)", doc.page_content.strip(), re.DOTALL)
            if match:
                qna_map[match.group(1).strip()].add(match.group(2).strip())
        return {q: sorted(answers) for q, answers in qna_map.items()}

    
    def _load_config(self, path: str) -> dict:
        if not os.path.exists(path):
            raise FileNotFoundError(f"Missing configuration file: {path}")
        with open(path, "r") as f:
            return yaml.safe_load(f)

    def _group_documents_by_domain(self, df: pd.DataFrame) -> Dict[str, List[LangchainDocument]]:
        grouped = defaultdict(list)
        for _, row in df.iterrows():
            content = f"Question: {row['question']}\nAnswer: {row['answer']}"
            metadata = {k: row[k] for k in [
                "theme", "tags", "source", "sourcefile", "domain", "sub_domain", "language"
            ]}
            key = f"{row['domain']}_{row['sub_domain']}".lower().replace(" ", "_")
            grouped[key].append(LangchainDocument(page_content=content, metadata=metadata))
        return grouped

    def clear_cache(self) -> None:
        """
        Clear the query cache and reset cache order.
        Saves current cache to disk before clearing if persistence is enabled.
        """
        if self._persist_cache and self._cache:
            self._save_persistent_cache()
        
        self._cache.clear()
        self._cache_order.clear()
        print(f"Query cache cleared. Cache size reset to 0.")

    def get_cache_stats(self) -> Dict[str, int]:
        """
        Get current cache statistics.
        
        Returns:
            Dict containing cache size, max size, and query count.
        """
        return {
            "current_size": len(self._cache),
            "max_size": self._cache_size,
            "query_count": self._query_count,
            "persistent_cache_enabled": self._persist_cache
        }
    
    def shutdown(self):
        """Clean shutdown - save cache if persistence is enabled."""
        if self._persist_cache:
            self._save_persistent_cache()

    def _deduplicate_documents(self, docs: List[LangchainDocument]) -> List[LangchainDocument]:
        seen, unique = set(), []
        for doc in docs:
            key = (doc.page_content.strip(), frozenset(doc.metadata.items()))
            if key not in seen:
                seen.add(key)
                unique.append(doc)
        return unique