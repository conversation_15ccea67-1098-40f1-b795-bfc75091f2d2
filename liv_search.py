"""
The Linkup search can output raw search results which can then be re-used in different use-cases,
for instance in a RAG system, with the output_type parameter set to "searchResults".
"""

from linkup import LinkupClient

client = LinkupClient(api_key="60dd18d0-2fa7-4e00-9a34-b14fb579c165")

response = client.search(
    query="Which is the largest P2P lending platform in India?",
    depth="standard",  # or "deep"
    output_type="sourcedAnswer",
)
print(type(response))
print(response)
