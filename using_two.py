import os
import sys
import asyncio
import logging
import traceback
import re
import time
import json
import random
import requests
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from contextlib import nullcontext
from livekit import rtc
from livekit.agents import cli, WorkerOptions, Agent, JobContext, AgentSession, metrics
from livekit.agents.voice.room_io import RoomInputOptions, RoomOutputOptions
from livekit.agents.llm import Chat<PERSON>ontext, ChatMessage, function_tool
from livekit.agents.voice import MetricsCollectedEvent
from livekit.agents.voice import Agent, AgentSession, RunContext
from livekit.plugins import (
    silero,
    google,
    noise_cancellation,
)
from clients.vector_db_client import VectorDBClient
from prompts import get_enhanced_system_prompt
from utils.performance_monitor import PerformanceMonitor, PerformanceContext
from livekit.plugins.google.beta.realtime.realtime_api import RealtimeModel

# Configure logging - let LiveKit CLI handle logging configuration
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 🔇 SUPPRESS VERBOSE DEBUG LOGS from external libraries
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)
logging.getLogger('requests').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('httpcore').setLevel(logging.WARNING)
logging.getLogger('transformers').setLevel(logging.WARNING)
logging.getLogger('huggingface_hub').setLevel(logging.WARNING)
logging.getLogger('sentence_transformers').setLevel(logging.WARNING)
logging.getLogger('torch').setLevel(logging.WARNING)
logging.getLogger('chromadb').setLevel(logging.WARNING)
logging.getLogger('openai').setLevel(logging.WARNING)
logging.getLogger('groq').setLevel(logging.WARNING)

# Keep important logs but reduce noise
logging.getLogger('livekit').setLevel(logging.INFO)
logging.getLogger('google').setLevel(logging.INFO)

# Load environment variables
load_dotenv()

# LiveKit components are already imported above
try:
    logger.info("✅ LiveKit components imported successfully")
except ImportError as e:
    logger.error(f"❌ Failed to import LiveKit components: {str(e)}")
    sys.exit(1)

# Import existing clients
from clients.vector_db_client import VectorDBClient

# Constants
BASE_DIR = Path(__file__).resolve().parent
VECTOR_DB_PATH = BASE_DIR / "vectordb" / "bge-m3"
VECTOR_CONFIG_PATH = BASE_DIR / "vectordb" / "vectordb_config.yml"

class P2PLendingFunctions:
    """Enhanced P2P lending functions with zero-latency optimization and knowledge base focus."""
    
    def __init__(self, vector_db_client: VectorDBClient):
        self.vector_db_client = vector_db_client
        
        # Load config for max_results
        import yaml
        try:
            with open("vectordb/vectordb_config.yml", 'r') as f:
                config = yaml.safe_load(f)
                self.default_max_results = config.get('retriever_defaults', {}).get('similarity', {}).get('k', 5)
        except Exception as e:
            logger.warning(f"Could not load config, using default max_results=5: {e}")
            self.default_max_results = 5
        
        self.collection_name = 'faqs_p2p'
        
        # Initialize vector store
        try:
            self.vector_db_client.load_existing_collections([self.collection_name])
            logger.info("✅ Vector database loaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to load vector database: {e}")

    def search_p2p_knowledge(self, user_query: str, max_results: int = None) -> List[Dict[str, Any]]:
        """
        Search the P2P lending knowledge base for relevant information.
        PRIMARY SOURCE OF TRUTH for all P2P queries - no hardcoded responses.
        
        Args:
            user_query: The user's question about P2P lending
            max_results: Maximum number of results to return (uses config default if None)
            
        Returns:
            List of relevant documents with content and metadata
        """
        try:
            start_time = time.time()
            
            # Use default from config if max_results not specified
            if max_results is None:
                default_k = self.vector_db_client._config.get('retriever_defaults', {}).get('similarity', {}).get('k', 3)
                max_results = default_k
            
            documents = self.vector_db_client.query_vectordb(
                collection_name=self.collection_name,
                user_query=user_query,
                retriever_type="similarity"
            )
            
            # Format results for function calling
            results = []
            for doc in documents[:max_results]:
                results.append({
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "relevance_score": "high"
                })
            
            search_time = time.time() - start_time
            logger.info(f"🔍 Vector search completed in {search_time:.2f}s, found {len(results)} results")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Error in vector search: {e}")
            return [{"content": "I'm having trouble accessing the knowledge base right now. Please try again.", "error": str(e)}]


class DualGeminiSession:
    """
    Handles dual Gemini model workflow:
    1. gemini-2.0-flash-live-001 for STT + tool calling/text generation
    2. gemini-2.5-flash-preview-native-audio-dialog for audio output
    """
    
    def __init__(self, p2p_functions: P2PLendingFunctions, instructions: str):
        self.p2p_functions = p2p_functions
        self.instructions = instructions
        
        # Available Gemini voices
        AVAILABLE_VOICES = {
            'male': ['Puck', 'Charon', 'Fenrir', 'Orus', 'Enceladus', 'Iapetus', 'Umbriel'],
            'female': ['Aoede', 'Kore', 'Leda', 'Zephyr', 'Autonoe', 'Callirrhoe', 'Despina', 'Erinome', 'Sadachbia']
        }
        selected_voice = 'Kore'
        
        # Model 1: Half-cascade for STT + Tool Calling (gemini-2.0-flash-live-001)
        self.tool_model = RealtimeModel(
            model="gemini-2.0-flash-live-001",
            language="hi-IN",
            voice=selected_voice,
            temperature=0.7,
            project=os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-project-459106'),
            location='us-central1'
        )
        
        # Model 2: Native audio for speech generation (gemini-2.5-flash-preview-native-audio-dialog)
        self.audio_model = RealtimeModel(
            model="gemini-2.5-flash-preview-native-audio-dialog",
            language="hi-IN", 
            voice=selected_voice,
            temperature=0.7,
            project=os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-project-459106'),
            location='us-central1'
        )
        
        logger.info("✅ Dual Gemini models initialized - Tool Model: gemini-2.0-flash-live-001, Audio Model: gemini-2.5-flash-preview-native-audio-dialog")
    
    async def process_user_input(self, user_speech: str, session: AgentSession) -> str:
        """
        Process user input through dual model workflow:
        1. Use tool_model for understanding + tool calling
        2. Use audio_model for final audio generation
        """
        try:
            logger.info(f"🎯 [DUAL MODEL WORKFLOW] Processing: '{user_speech[:50]}...'")
            
            # Step 1: Use gemini-2.0-flash-live-001 for tool calling/text generation
            logger.info("🔧 [STEP 1] Using gemini-2.0-flash-live-001 for tool analysis...")
            
            # Check if this requires tool calling (knowledge search)
            requires_knowledge = await self._needs_knowledge_search(user_speech)
            
            if requires_knowledge:
                logger.info("🔍 [TOOL CALLING] Knowledge search required")
                # Execute tool call
                search_results = self.p2p_functions.search_p2p_knowledge(user_speech, max_results=3)
                
                # Create context with search results
                context_text = self._format_search_results(search_results)
                response_text = await self._generate_response_with_context(user_speech, context_text)
            else:
                logger.info("💬 [DIRECT RESPONSE] No tool calling needed")
                # Direct response generation
                response_text = await self._generate_direct_response(user_speech)
            
            # Step 2: Use gemini-2.5-flash-preview-native-audio-dialog for audio generation
            logger.info("🎵 [STEP 2] Using gemini-2.5-flash-preview-native-audio-dialog for audio generation...")
            
            # Generate audio using native audio model
            await self._generate_audio_response(response_text, session)
            
            return response_text
            
        except Exception as e:
            logger.error(f"❌ Error in dual model workflow: {e}")
            error_response = "I apologize, I'm having technical difficulties. Please try again."
            await self._generate_audio_response(error_response, session)
            return error_response
    
    async def _needs_knowledge_search(self, user_input: str) -> bool:
        """Determine if user input requires knowledge base search."""
        # Simple keyword-based detection for P2P lending queries
        p2p_keywords = [
            'p2p', 'peer to peer', 'lending', 'investment', 'returns', 'risk',
            'lendenclub', 'borrower', 'lender', 'interest', 'loan', 'credit'
        ]
        
        user_lower = user_input.lower()
        return any(keyword in user_lower for keyword in p2p_keywords)
    
    def _format_search_results(self, search_results: List[Dict[str, Any]]) -> str:
        """Format search results for context."""
        if not search_results:
            return "No relevant information found in knowledge base."
        
        formatted = "Knowledge Base Information:\n"
        for i, result in enumerate(search_results, 1):
            content = result.get('content', '').strip()
            if content:
                formatted += f"{i}. {content}\n"
        
        return formatted
    
    async def _generate_response_with_context(self, user_query: str, context: str) -> str:
        """Generate response using tool model with search context."""
        try:
            # Create prompt with context
            prompt = f"""
            {self.instructions}
            
            User Query: {user_query}
            
            Context from Knowledge Base:
            {context}
            
            Please provide a helpful response based on the context provided.
            """
            
            # Use tool model for response generation
            # Note: This is a simplified implementation - you may need to adapt based on your RealtimeModel API
            response = await self._call_tool_model(prompt)
            return response
            
        except Exception as e:
            logger.error(f"❌ Error generating response with context: {e}")
            return "I found some information but had trouble processing it. Could you rephrase your question?"
    
    async def _generate_direct_response(self, user_input: str) -> str:
        """Generate direct response using tool model."""
        try:
            prompt = f"""
            {self.instructions}
            
            User: {user_input}
            
            Please provide a helpful response.
            """
            
            response = await self._call_tool_model(prompt)
            return response
            
        except Exception as e:
            logger.error(f"❌ Error generating direct response: {e}")
            return "I understand your question, but I'm having trouble generating a response right now."
    
    async def _call_tool_model(self, prompt: str) -> str:
        """Call the tool model (gemini-2.0-flash-live-001) for text generation."""
        try:
            # This is a placeholder - implement based on your RealtimeModel API
            # You may need to adapt this based on how RealtimeModel handles text generation
            logger.info("🔧 [TOOL MODEL] Calling gemini-2.0-flash-live-001...")
            
            # Placeholder response generation - replace with actual API call
            response = "This is a placeholder response from the tool model. Please implement the actual API call based on your RealtimeModel interface."
            
            logger.info(f"✅ [TOOL MODEL] Response generated: {len(response)} chars")
            return response
            
        except Exception as e:
            logger.error(f"❌ Error calling tool model: {e}")
            return "I apologize, I'm having trouble processing your request."
    
    async def _generate_audio_response(self, text: str, session: AgentSession) -> None:
        """Generate audio using native audio model (gemini-2.5-flash-preview-native-audio-dialog)."""
        try:
            logger.info("🎵 [AUDIO MODEL] Generating audio with gemini-2.5-flash-preview-native-audio-dialog...")
            
            # Use session to generate audio response with native audio model
            # The audio model will handle the text-to-speech conversion
            session.generate_reply(instructions=f"Please speak this text naturally: {text}")
            
            logger.info("✅ [AUDIO MODEL] Audio generation initiated")
            
        except Exception as e:
            logger.error(f"❌ Error generating audio response: {e}")
            # Fallback to simple text response
            session.generate_reply(instructions="I apologize for the technical difficulty.")


class NeuraVoiceV2(Agent):
    """
    NeuraVoice V2 with Dual Gemini Model Architecture
    """
    
    def __init__(self):
        # Initialize conversation memory and performance metrics
        self.performance_metrics = {
            'total_queries': 0,
            'function_calls': 0,
            'vector_searches': 0,
            'avg_response_time': 0.0
        }
        
        # 📊 COMPREHENSIVE METRICS COLLECTION
        self.usage_collector = metrics.UsageCollector()
        self.query_metrics = []
        self.current_query_start_time = None
        self.current_query_text = None
        
        # Validate environment and initialize components
        self._validate_environment()
        
        # Initialize vector DB and P2P functions
        self.vector_db_client = VectorDBClient(
            db_path=VECTOR_DB_PATH,
            config_path=VECTOR_CONFIG_PATH
        )
        self.p2p_functions = P2PLendingFunctions(self.vector_db_client)
        
        # Create enhanced instructions
        enhanced_instructions = self._create_corrected_instructions()
        
        # Initialize dual Gemini session
        self.dual_session = DualGeminiSession(self.p2p_functions, enhanced_instructions)
        
        # Use the audio model as the primary LLM for the Agent
        # This ensures LiveKit integration works properly
        super().__init__(
            instructions=enhanced_instructions,
            llm=self.dual_session.audio_model,  # Use native audio model as primary
            allow_interruptions=True
        )
        
        logger.info("✅ NeuraVoice V2 initialized with Dual Gemini Model Architecture!")

    async def on_enter(self):
        """Called when agent enters the session - provide greeting."""
        import random
        
        greeting_variations = [
            "Mention who you are and greet the user in short sentence",
            "Mention who you are and greet the user and talk about the purpose of the call in short sentence",
            "Introduce yourself briefly as Priya from LendenClub and greet the user in English",
            "Greet the user warmly, mention you're Priya from LendenClub calling about P2P lending"
        ]
        
        selected_greeting = random.choice(greeting_variations)
        logger.info("🎯 [ON_ENTER] Attempting to generate initial greeting")
        logger.info(f"🎯 [ON_ENTER] Selected greeting instruction: {selected_greeting}")
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.session.generate_reply(instructions=selected_greeting)
                logger.info("✅ [ON_ENTER] Greeting generation initiated successfully")
                await asyncio.sleep(5.0)
                return
                
            except (ZeroDivisionError, TypeError) as e:
                logger.warning(f"⚠️ [ON_ENTER] LiveKit metrics bug encountered (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(0.5)
                    continue
                else:
                    logger.error("❌ [ON_ENTER] All greeting attempts failed due to metrics bug")
                    
            except Exception as e:
                logger.error(f"❌ [ON_ENTER] Unexpected error in greeting generation: {e}")
                break
        
        logger.info("ℹ️ [ON_ENTER] Agent ready for user input")

    async def on_user_speech_committed(self, user_speech: str):
        """Called when user speech is committed - process through dual model workflow."""
        logger.info(f"👤 [USER SPEECH COMMITTED] Processing: '{user_speech[:50]}...'")
        self.start_query_metrics(user_speech)
        
        try:
            # Process through dual model workflow
            response = await self.dual_session.process_user_input(user_speech, self.session)
            logger.info(f"✅ [DUAL MODEL WORKFLOW] Completed successfully")
            
        except Exception as e:
            logger.error(f"❌ [DUAL MODEL WORKFLOW] Error: {e}")
        
        # Call parent method if it exists
        if hasattr(super(), 'on_user_speech_committed'):
            await super().on_user_speech_committed(user_speech)

    async def on_agent_speech_committed(self, agent_speech: str):
        """Called when agent speech is committed - end metrics tracking."""
        logger.info(f"🤖 [AGENT SPEECH COMMITTED] Ending metrics for: '{agent_speech[:50]}...'")
        self.end_query_metrics(agent_speech)
        
        # Call parent method if it exists
        if hasattr(super(), 'on_agent_speech_committed'):
            await super().on_agent_speech_committed(agent_speech)

    def log_startup_status(self):
        """Log comprehensive startup status."""
        logger.info("="*80)
        logger.info("🚀 [NEURAVOICE V2 STARTUP] Dual Gemini Model System initialization complete!")
        logger.info("="*80)
        logger.info("🔧 [TOOL MODEL] gemini-2.0-flash-live-001 (Half-cascade STT + Tool Calling)")
        logger.info("🎵 [AUDIO MODEL] gemini-2.5-flash-preview-native-audio-dialog (Native Audio Output)")
        logger.info("🔍 [VECTOR DB] Knowledge base loaded and tested")
        logger.info("🌐 [LANGUAGE] Multi-language support: English, Hindi, Hinglish")
        logger.info("="*80)
        logger.info("🎯 [WORKFLOW] User Voice → Tool Model → Tool Execution → Audio Model → Audio Output")
        logger.info("="*80)
        logger.info("✅ [SYSTEM READY] NeuraVoice V2 Dual Model Architecture is FULLY OPERATIONAL!")
        logger.info("="*80)

    def _validate_environment(self):
        """Validate required environment variables."""
        required_vars = {
            'GOOGLE_APPLICATION_CREDENTIALS': os.getenv('GOOGLE_APPLICATION_CREDENTIALS'),
            'GOOGLE_CLOUD_PROJECT': os.getenv('GOOGLE_CLOUD_PROJECT')
        }
        
        if not required_vars['GOOGLE_CLOUD_PROJECT']:
            os.environ['GOOGLE_CLOUD_PROJECT'] = 'ai-project-459106'
            required_vars['GOOGLE_CLOUD_PROJECT'] = 'ai-project-459106'
        
        missing_vars = [var for var, value in required_vars.items() if not value]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        logger.info("✅ Environment variables validated")
    
    def _create_corrected_instructions(self) -> str:
        """Create instructions for the LLM using the enhanced system prompt."""
        return get_enhanced_system_prompt()
    
    @function_tool
    async def search_knowledge_base(self, context: RunContext, user_query: str, max_results: int = 3):
        """Search P2P lending knowledge base - used by tool model."""
        logger.info(f"🔍 Searching KB for: '{user_query[:50]}...'")
        
        self.performance_metrics['function_calls'] += 1
        self.performance_metrics['vector_searches'] += 1
        
        try:
            start_time = time.time()
            results = self.p2p_functions.search_p2p_knowledge(user_query, max_results)
            search_duration = time.time() - start_time
            
            logger.info(f"✅ KB search completed: {len(results)} results in {search_duration:.2f}s")
            
            if not results:
                return [{"content": "I don't have specific information about that. Could you rephrase your question?", "source": "fallback"}]
            
            formatted_results = []
            for i, result in enumerate(results, 1):
                formatted_results.append({
                    "content": result.get('content', ''),
                    "source": result.get('metadata', {}).get('source', 'Knowledge Base'),
                    "result_number": i
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"❌ KB search error: {e}")
            return [{"content": f"Search error: {str(e)}", "source": "error"}]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        return {
            'total_queries': self.performance_metrics['total_queries'],
            'function_calls': self.performance_metrics['function_calls'],
            'vector_searches': self.performance_metrics['vector_searches'],
            'avg_response_time': self.performance_metrics['avg_response_time']
        }

    def start_query_metrics(self, user_input: str):
        """Start tracking metrics for a new user query."""
        self.current_query_start_time = time.time()
        self.current_query_text = user_input
        logger.info(f"📊 [QUERY METRICS START] Query: '{user_input[:50]}...'")
        self.performance_metrics['total_queries'] += 1

    def end_query_metrics(self, assistant_response: str):
        """End tracking metrics for the current query."""
        if self.current_query_start_time is None:
            return
        
        end_time = time.time()
        total_time = end_time - self.current_query_start_time
        
        usage_summary = self.usage_collector.get_summary()
        
        query_metric = {
            'timestamp': datetime.now().isoformat(),
            'user_query': self.current_query_text,
            'assistant_response': assistant_response,
            'total_time_seconds': total_time,
            'usage_summary': usage_summary,
            'query_number': len(self.query_metrics) + 1
        }
        
        self.query_metrics.append(query_metric)
        
        logger.info("="*120)
        logger.info(f"📊 [QUERY METRICS COMPLETE] Query #{query_metric['query_number']}")
        logger.info(f"👤 [USER INPUT]: {self.current_query_text}")
        logger.info(f"🤖 [ASSISTANT RESPONSE]: {assistant_response}")
        logger.info(f"⏱️ [TOTAL TIME]: {total_time:.3f} seconds")
        logger.info("="*120)
        
        # Reset for next query
        self.current_query_start_time = None
        self.current_query_text = None

        # Update running average response time
        queries_so_far = self.performance_metrics['total_queries']
        if queries_so_far:
            prev_avg = self.performance_metrics['avg_response_time']
            self.performance_metrics['avg_response_time'] = ((prev_avg * (queries_so_far - 1)) + total_time) / queries_so_far

    def on_metrics_collected(self, event: MetricsCollectedEvent):
        """Handle metrics collection events."""
        logger.info(f"📊 [METRICS COLLECTED] {event.metrics}")
        self.usage_collector.collect(event.metrics)

    def log_final_usage_summary(self):
        """Log final usage summary."""
        summary = self.usage_collector.get_summary()
        logger.info("="*120)
        logger.info("📊 [FINAL USAGE SUMMARY] - Dual Gemini Model Architecture")
        logger.info("="*120)
        logger.info(f"📈 Total Queries Processed: {len(self.query_metrics)}")
        logger.info(f"🔧 Tool Model Calls: gemini-2.0-flash-live-001")
        logger.info(f"🎵 Audio Model Calls: gemini-2.5-flash-preview-native-audio-dialog")
        
        if summary:
            for service, metrics in summary.items():
                logger.info(f"🔧 {service.upper()} USAGE:")
                if isinstance(metrics, dict):
                    for metric_name, value in metrics.items():
                        logger.info(f"  • {metric_name}: {value}")
                else:
                    logger.info(f"  • Total: {metrics}")
        
        if self.query_metrics:
            avg_time = sum(q['total_time_seconds'] for q in self.query_metrics) / len(self.query_metrics)
            logger.info(f"⏱️ Average Response Time: {avg_time:.3f}s")
        
        logger.info("="*120)


async def entrypoint(ctx: JobContext):
    """Entrypoint with dual Gemini model setup."""
    logger.info("🚀 Starting NeuraVoice V2 with Dual Gemini Model Architecture...")
    
    try:
        # Connect to LiveKit server
        await ctx.connect()
        logger.info("✅ Connected to LiveKit server")

        # Create NeuraVoice V2 bot instance with dual models
        bot = NeuraVoiceV2()
        logger.info("✅ NeuraVoice V2 with Dual Gemini Models created")

        # Create AgentSession
        session = AgentSession()
        logger.info("✅ Created AgentSession for Dual Model workflow")

        # 📊 SET UP COMPREHENSIVE METRICS COLLECTION
        @session.on("metrics_collected")
        def _on_metrics_collected(event: MetricsCollectedEvent):
            """Handle metrics collection events."""
            metrics.log_metrics(event.metrics)
            bot.on_metrics_collected(event)

        # 📊 SET UP SHUTDOWN CALLBACK
        async def log_final_usage():
            """Log final usage summary and cleanup."""
            bot.log_final_usage_summary()
            if hasattr(bot, 'vector_db_client'):
                bot.vector_db_client.shutdown()
                logger.info("💾 Vector DB cache saved")

        ctx.add_shutdown_callback(log_final_usage)
        logger.info("✅ Metrics collection and shutdown callbacks configured")
    
        # Log startup status
        bot.log_startup_status()

        # Start the session with the agent
        await session.start(
            agent=bot,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                noise_cancellation=noise_cancellation.BVC()
            ),
            room_output_options=RoomOutputOptions(
                audio_enabled=True
            )
        )

        logger.info("✅ AgentSession started with NeuraVoice V2 Dual Gemini Model Architecture")
        
    except Exception as e:
        logger.exception(f"❌ Error in entrypoint: {str(e)}")
        raise

if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))