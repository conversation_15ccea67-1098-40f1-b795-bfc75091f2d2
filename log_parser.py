#!/usr/bin/env python3
"""
Log Parser Script - Extract User Queries and Assistant Responses
Parses various log formats and creates a pandas DataFrame with user-assistant conversation pairs.
"""

import pandas as pd
import re
import os
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import argparse

class LogParser:
    """Parse conversation logs and extract user-assistant pairs."""
    
    def __init__(self):
        # Patterns to match user and assistant messages in different log formats
        self.patterns = {
            # Pattern 1: [USER INPUT]: query / [ASSISTANT RESPONSE]: response
            'livekit_metrics': {
                'user': re.compile(r'\[USER INPUT\]:\s*(.+?)(?=\s*(?:\d{4}-\d{2}-\d{2}|\[|$))', re.DOTALL),
                'assistant': re.compile(r'\[ASSISTANT RESPONSE\]:\s*(.+?)(?=\s*(?:\d{4}-\d{2}-\d{2}|\[|$))', re.DOTALL)
            },
            
            # Pattern 2: 👤 [USER]: query / 🤖 [ASSISTANT]: response
            'emoji_format': {
                'user': re.compile(r'👤\s*\[.*?User.*?\]:\s*(.+?)(?=\s*(?:\d{4}-\d{2}-\d{2}|👤|🤖|\[|$))', re.DOTALL | re.IGNORECASE),
                'assistant': re.compile(r'🤖\s*\[.*?Assistant.*?\]:\s*(.+?)(?=\s*(?:\d{4}-\d{2}-\d{2}|👤|🤖|\[|$))', re.DOTALL | re.IGNORECASE)
            },
            
            # Pattern 3: User: query / Assistant: response
            'simple_format': {
                'user': re.compile(r'User:\s*(.+?)(?=\s*(?:Assistant:|User:|\d{4}-\d{2}-\d{2}|$))', re.DOTALL),
                'assistant': re.compile(r'Assistant:\s*(.+?)(?=\s*(?:User:|Assistant:|\d{4}-\d{2}-\d{2}|$))', re.DOTALL)
            },
            
            # Pattern 4: User said: query / TTS OUTPUT response
            'detailed_format': {
                'user': re.compile(r'User said:\s*[\'"](.+?)[\'"]', re.DOTALL),
                'assistant': re.compile(r'🗣️\s*[\'"](.+?)[\'"]|TTS OUTPUT.*?[\'"](.+?)[\'"]', re.DOTALL)
            }
        }
    
    def clean_text(self, text: str) -> str:
        """Clean extracted text by removing extra whitespace and newlines."""
        if not text:
            return ""
        
        # Remove excessive whitespace and newlines
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # Remove common log prefixes/suffixes
        cleaned = re.sub(r'^.*?:\s*', '', cleaned)
        cleaned = re.sub(r'\s*\(\w+:\s*\d+\)$', '', cleaned)
        
        return cleaned.strip()
    
    def parse_log_file(self, file_path: str) -> pd.DataFrame:
        """Parse a log file and return a DataFrame with conversations."""
        print(f"Parsing log file: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Log file not found: {file_path}")
        
        conversations = []
        
        try:
            # Read file in chunks for large files
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read()
                
            # Try each pattern
            for pattern_name, patterns in self.patterns.items():
                user_matches = patterns['user'].findall(content)
                assistant_matches = patterns['assistant'].findall(content)
                
                print(f"Pattern '{pattern_name}': Found {len(user_matches)} user messages, {len(assistant_matches)} assistant messages")
                
                # For detailed_format, handle tuple matches
                if pattern_name == 'detailed_format' and assistant_matches:
                    # assistant_matches might be tuples, flatten them
                    assistant_texts = []
                    for match in assistant_matches:
                        if isinstance(match, tuple):
                            assistant_texts.append(''.join(match))
                        else:
                            assistant_texts.append(match)
                    assistant_matches = assistant_texts
                
                # Pair up user and assistant messages
                min_length = min(len(user_matches), len(assistant_matches))
                
                for i in range(min_length):
                    user_text = self.clean_text(user_matches[i])
                    assistant_text = self.clean_text(assistant_matches[i])
                    
                    if len(user_text) > 3 and len(assistant_text) > 3:  # Filter very short messages
                        conversations.append({
                            'user_query': user_text,
                            'assistant_response': assistant_text,
                            'pattern_used': pattern_name,
                            'conversation_id': len(conversations) + 1
                        })
        
        except Exception as e:
            print(f"Error reading file: {e}")
            return pd.DataFrame()
        
        if conversations:
            df = pd.DataFrame(conversations)
            # Remove duplicates
            df = df.drop_duplicates(subset=['user_query', 'assistant_response'], keep='first')
            df = df.reset_index(drop=True)
            df['conversation_id'] = range(1, len(df) + 1)
            
            print(f"Extracted {len(df)} unique conversations")
            return df
        else:
            print("No conversations found")
            return pd.DataFrame(columns=['user_query', 'assistant_response', 'pattern_used', 'conversation_id'])

def main():
    """Main function to run the log parser."""
    # Initialize log parser
    log_parser = LogParser()
    
    # Default to logs.txt in current directory
    log_file = "logs.txt"
    
    if os.path.exists(log_file):
        print(f"Parsing log file: {log_file}")
        df = log_parser.parse_log_file(log_file)
        
        if not df.empty:
            # Save to CSV
            output_file = "conversations.csv"
            df.to_csv(output_file, index=False, encoding='utf-8')
            print(f"\nConversations saved to: {output_file}")
            
            # Print summary
            print("\n" + "="*60)
            print("EXTRACTION SUMMARY")
            print("="*60)
            print(f"Total conversations: {len(df)}")
            print(f"Average user query length: {df['user_query'].str.len().mean():.1f} characters")
            print(f"Average assistant response length: {df['assistant_response'].str.len().mean():.1f} characters")
            
            # Show pattern usage
            if 'pattern_used' in df.columns:
                print("\nPattern usage:")
                pattern_counts = df['pattern_used'].value_counts()
                for pattern, count in pattern_counts.items():
                    print(f"  {pattern}: {count} conversations")
            
            # Show first few conversations
            print("\nFirst 3 conversations:")
            print("-" * 60)
            for i, row in df.head(3).iterrows():
                print(f"Conversation {row['conversation_id']}:")
                print(f"  User: {row['user_query'][:100]}{'...' if len(row['user_query']) > 100 else ''}")
                print(f"  Assistant: {row['assistant_response'][:100]}{'...' if len(row['assistant_response']) > 100 else ''}")
                print()
                
        else:
            print("No conversations found in the log file")
    else:
        print(f"Log file '{log_file}' not found in current directory")
        print("Place your log file as 'logs.txt' in the same directory as this script")

if __name__ == "__main__":
    main() 