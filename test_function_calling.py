#!/usr/bin/env python3
"""
Test script to verify function calling with different Gemini models
"""

import os
import asyncio
import logging
from livekit.agents.llm import function_tool
from livekit.plugins.google.beta.realtime.realtime_api import RealtimeModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FunctionCallTester:
    """Test function calling with different Gemini models."""
    
    def __init__(self):
        self.test_results = {}
    
    @function_tool
    async def test_function(self, query: str) -> str:
        """Test function to verify function calling works.
        
        Args:
            query: Test query string
        """
        logger.info(f"🎯 TEST FUNCTION CALLED! Query: '{query}'")
        return f"Function calling successful! Received: {query}"
    
    async def test_model(self, model_name: str, **model_kwargs):
        """Test a specific model configuration."""
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing model: {model_name}")
        logger.info(f"Configuration: {model_kwargs}")
        logger.info(f"{'='*60}")
        
        try:
            # Create model
            model = RealtimeModel(
                model=model_name,
                project=os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-project-459106'),
                location='us-central1',
                **model_kwargs
            )
            logger.info(f"✅ Model created successfully")
            
            # Test basic functionality
            # Note: This is a simplified test - full testing would require LiveKit session
            self.test_results[model_name] = {
                'model_creation': 'SUCCESS',
                'configuration': model_kwargs,
                'notes': 'Model created without errors'
            }
            
        except Exception as e:
            logger.error(f"❌ Model creation failed: {e}")
            self.test_results[model_name] = {
                'model_creation': 'FAILED',
                'error': str(e),
                'configuration': model_kwargs
            }
    
    async def run_tests(self):
        """Run comprehensive tests on different model configurations."""
        
        # Test configurations
        test_configs = [
            {
                'model': 'gemini-2.5-flash-preview-native-audio-dialog',
                'config': {'voice': 'Kore', 'temperature': 0.7},
                'description': '2.5-flash with basic config'
            },
            {
                'model': 'gemini-2.5-flash-preview-native-audio-dialog', 
                'config': {'voice': 'Kore', 'temperature': 0.7, 'tools': 'auto'},
                'description': '2.5-flash with tools=auto'
            },
            {
                'model': 'gemini-2.5-flash-preview-native-audio-dialog',
                'config': {'voice': 'Kore', 'temperature': 0.7, 'tools': 'auto', 'tool_choice': 'auto'},
                'description': '2.5-flash with tools and tool_choice'
            },
            {
                'model': 'gemini-2.0-flash-exp',
                'config': {'voice': 'Kore', 'temperature': 0.7},
                'description': '2.0-flash baseline (known working)'
            }
        ]
        
        logger.info("🧪 Starting Gemini Function Calling Tests")
        logger.info("="*80)
        
        for test_config in test_configs:
            logger.info(f"\n📋 Test: {test_config['description']}")
            await self.test_model(test_config['model'], **test_config['config'])
        
        # Print summary
        self.print_test_summary()
    
    def print_test_summary(self):
        """Print comprehensive test summary."""
        logger.info("\n" + "="*80)
        logger.info("🏁 TEST SUMMARY")
        logger.info("="*80)
        
        for model, results in self.test_results.items():
            status = "✅ PASS" if results['model_creation'] == 'SUCCESS' else "❌ FAIL"
            logger.info(f"{status} {model}")
            
            if results['model_creation'] == 'SUCCESS':
                logger.info(f"   Configuration: {results['configuration']}")
                logger.info(f"   Notes: {results['notes']}")
            else:
                logger.info(f"   Error: {results['error']}")
        
        # Recommendations
        logger.info("\n" + "="*80)
        logger.info("💡 RECOMMENDATIONS")
        logger.info("="*80)
        
        successful_models = [model for model, results in self.test_results.items() 
                           if results['model_creation'] == 'SUCCESS']
        
        if 'gemini-2.5-flash-preview-native-audio-dialog' in successful_models:
            logger.info("✅ gemini-2.5-flash-preview-native-audio-dialog can be used")
            logger.info("   Try the basic configuration first (no explicit tools parameter)")
            logger.info("   The @function_tool decorators should be auto-detected")
        else:
            logger.info("⚠️ gemini-2.5-flash-preview-native-audio-dialog failed")
            logger.info("   Recommend using gemini-2.0-flash-exp for reliable function calling")
        
        logger.info("\n🔧 To use 2.0-flash as fallback, set environment variable:")
        logger.info("   export USE_GEMINI_2_0_FALLBACK=true")

async def main():
    """Main test function."""
    tester = FunctionCallTester()
    await tester.run_tests()

if __name__ == "__main__":
    asyncio.run(main()) 