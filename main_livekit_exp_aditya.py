import os
import sys
import asyncio
import logging
import traceback
import re
import time
import json
import random
import requests
from typing import List, Dict, <PERSON>, <PERSON><PERSON>, Tu<PERSON>
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from contextlib import nullcontext
from livekit import rtc
from livekit.agents import cli, WorkerOptions, Agent, JobContext, AgentSession, metrics
from livekit.agents.voice.room_io import RoomInputOptions, RoomOutputOptions
from livekit.agents.llm import Chat<PERSON>ontex<PERSON>, ChatMessage, function_tool
from livekit.agents.voice import MetricsCollectedEvent
from livekit.agents.voice import Agent, AgentSession, RunContext
from livekit.plugins import (
    elevenlabs,
    aws,
    groq,
    silero,
    google,
    cartesia,
    noise_cancellation,
    deepgram
)
from clients.vector_db_client import VectorDBClient
from config.templates.system_prompts import get_unified_sys_prompt # get_core_sys_prompt, get_ooc_sys_prompt, 
from config.templates.user_prompts import get_core_user_prompt, get_ooc_user_prompt
from utils.performance_monitor import PerformanceMonitor, PerformanceContext
from livekit.plugins.google.beta.realtime.realtime_api import RealtimeModel

# Configure logging
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
# )
# logger = logging.getLogger(__name__)

# Configure logging - let LiveKit CLI handle logging configuration
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 🔇 SUPPRESS VERBOSE DEBUG LOGS from external libraries
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)
logging.getLogger('requests').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('httpcore').setLevel(logging.WARNING)
logging.getLogger('transformers').setLevel(logging.WARNING)
logging.getLogger('huggingface_hub').setLevel(logging.WARNING)
logging.getLogger('sentence_transformers').setLevel(logging.WARNING)
logging.getLogger('torch').setLevel(logging.WARNING)
logging.getLogger('chromadb').setLevel(logging.WARNING)
logging.getLogger('openai').setLevel(logging.WARNING)
logging.getLogger('groq').setLevel(logging.WARNING)

# Keep important logs but reduce noise
logging.getLogger('livekit').setLevel(logging.INFO)
logging.getLogger('google').setLevel(logging.INFO)


# Load environment variables
load_dotenv()

# LiveKit components are already imported above
try:
    logger.info("✅ LiveKit components imported successfully")
except ImportError as e:
    logger.error(f"❌ Failed to import LiveKit components: {str(e)}")
    sys.exit(1)

# Note: With Google Realtime API, we don't need separate TTS
# The Realtime API handles voice synthesis natively

# Import existing clients
from clients.vector_db_client import VectorDBClient
from config.templates.user_prompts import get_core_user_prompt, get_ooc_user_prompt

# Constants
BASE_DIR = Path(__file__).resolve().parent
VECTOR_DB_PATH = BASE_DIR / "vectordb" / "bge-m3"
VECTOR_CONFIG_PATH = BASE_DIR / "vectordb" / "vectordb_config.yml"

# =============================================
# SOPHISTICATED PERSONALITY COMPONENTS
# Integrated from witty_voice_bot.py
# =============================================

class DynamicPersonality:
    """Manages dynamic personality traits and witty responses."""
    
    def __init__(self):
        # Core personality dimensions that evolve
        self.traits = {
            'wit_level': 0.7,        # How witty/clever responses are
            'enthusiasm': 0.6,       # Energy level in responses
            'curiosity': 0.8,        # How much to ask follow-ups
            'warmth': 0.8,          # Friendliness level
            'playfulness': 0.5,      # Tendency for humor/jokes
            'directness': 0.6,       # How straightforward vs subtle
            'sophistication': 0.7,   # Vocabulary complexity
            'empathy_sensitivity': 0.9  # Emotional responsiveness
        }
        
        # Dynamic response pools that change based on context
        self.conversation_state = {
            'mood': 'neutral',
            'energy_level': 'medium',
            'topic_engagement': 'standard',
            'user_familiarity': 0.0,  # Increases over time
            'conversation_depth': 'surface'
        }
        
        # Sophisticated micro-interactions based on personality
        self.witty_transitions = {
            'en': {
                'topic_shift': [
                    "Speaking of which...", "That reminds me...", "Interesting you mention that...",
                    "Actually, that's fascinating because...", "Now that's curious...",
                    "You know what's funny about that?", "Here's what I find intriguing..."
                ],
                'agreement': [
                    "Absolutely!", "You're spot on!", "Couldn't agree more!",
                    "Now you're talking!", "Exactly my thoughts!", "You took the words right out of my mouth!"
                ],
                'mild_disagreement': [
                    "Well, here's another angle...", "I see it slightly differently...",
                    "That's one way to look at it, though...", "Hmm, what if we consider...",
                    "Playing devil's advocate here..."
                ],
                'surprise': [
                    "No way!", "Really? That's wild!", "Hold up, that's amazing!",
                    "Wait, what?", "Are you serious?", "That's incredible!"
                ],
                'thinking': [
                    "Let me think about this...", "That's a great question...",
                    "Hmm, you've got me thinking...", "Now that's worth pondering...",
                    "Give me a second here..."
                ]
            },
            'hi': {
                'topic_shift': [
                    "Arey haan, baat yeh hai...", "Iske baare mein...", "Interesting baat hai...",
                    "Actually, mujhe lagta hai...", "Dekho, yeh point hai..."
                ],
                'agreement': [
                    "Bilkul sahi!", "Absolutely right!", "Yahi toh main kehna chahti thi!",
                    "Perfect point!", "Exactly!"
                ],
                'surprise': [
                    "Arey waah!", "Sach mein?", "Kya baat hai!",
                    "No way!", "That's amazing!"
                ]
            }
        }
        
        # Context-aware filler reduction system
        self.last_filler_used = None
        self.filler_cooldown = 0
        self.filler_frequency = 0.15  # Start with 15% chance, will adapt
        
    def should_use_filler(self) -> bool:
        """Intelligent filler usage based on context and cooldown."""
        self.filler_cooldown = max(0, self.filler_cooldown - 1)
        
        # No fillers if we just used one
        if self.filler_cooldown > 0:
            return False
            
        # Reduce filler frequency based on conversation flow
        adjusted_frequency = self.filler_frequency * self.traits['directness']
        
        if random.random() < adjusted_frequency:
            self.filler_cooldown = random.randint(2, 5)  # Cooldown for 2-5 responses
            return True
        return False
    
    def get_personality_prompt_injection(self) -> str:
        """Generate personality-aware prompt injection."""
        wit_instruction = ""
        if self.traits['wit_level'] > 0.6:
            wit_instruction = "Be clever and witty in your responses. Use wordplay when appropriate."
        
        curiosity_instruction = ""
        if self.traits['curiosity'] > 0.7:
            curiosity_instruction = "Ask thoughtful follow-up questions that show genuine interest."
            
        playfulness_instruction = ""
        if self.traits['playfulness'] > 0.6:
            playfulness_instruction = "Don't be afraid to be playful or gently humorous when the mood is right."
            
        return f"{wit_instruction} {curiosity_instruction} {playfulness_instruction}".strip()

class SophisticatedMicroInteractions:
    """Advanced micro-interactions with variety and intelligence."""
    
    def __init__(self, personality: DynamicPersonality):
        self.personality = personality
        
        # Sophisticated backchanneling with variety
        self.advanced_backchannels = {
            'en': {
                'light_agreement': ["mm-hmm", "right", "yeah", "exactly", "for sure"],
                'strong_agreement': ["absolutely", "completely", "exactly right", "couldn't agree more"],
                'curiosity': ["really?", "no kidding?", "that's interesting", "tell me more"],
                'empathy_light': ["I hear you", "that makes sense", "I can imagine"],
                'empathy_strong': ["that sounds tough", "I really feel for you", "that must be difficult"],
                'processing': ["interesting", "let me think", "that's a good point", "hmm"],
                'encouragement': ["go on", "and then?", "what happened next?", "I'm listening"]
            },
            'hi': {
                'light_agreement': ["haan", "theek hai", "sahi", "bilkul"],
                'strong_agreement': ["bilkul sahi", "absolutely", "completely agree"],
                'curiosity': ["sach mein?", "really?", "interesting", "aur batao"],
                'empathy_light': ["samjha", "makes sense", "achha"],
                'empathy_strong': ["mushkil lag raha", "tough situation", "samajh gaya"],
                'processing': ["interesting", "good point", "sochta hoon"],
                'encouragement': ["aur?", "phir?", "and then?", "sun raha hoon"]
            }
        }
        
        # Intelligent conversation repair
        self.sophisticated_repairs = {
            'en': {
                'gentle_clarification': [
                    "I want to make sure I understand you correctly...",
                    "Just to clarify what you meant...",
                    "Could you help me understand that better?",
                    "I'm not quite following that part..."
                ],
                'confident_partial': [
                    "I think you said... but let me confirm",
                    "If I caught that right, you're saying...",
                    "So what I'm hearing is..."
                ],
                'graceful_confusion': [
                    "I'm drawing a blank here - could you rephrase that?",
                    "You've stumped me! Can you try that again?",
                    "My brain just hiccupped - one more time?"
                ]
            },
            'hi': {
                'gentle_clarification': [
                    "Thoda aur explain kar sakte hain?",
                    "Samjha nahi completely...",
                    "Could you clarify that?"
                ],
                'graceful_confusion': [
                    "Sorry, samjha nahi. Phir se?",
                    "Confusion ho gaya, try again?",
                    "Brain freeze! One more time?"
                ]
            }
        }
        
        # Usage tracking to prevent repetition
        self.recent_backchannels = []
        self.recent_repairs = []
        self.max_recent_items = 5
    
    def get_contextual_backchannel(self, emotion: str, intensity: str, lang: str) -> Optional[str]:
        """Get intelligent backchannel based on context."""
        if not self._should_backchannel():
            return None
            
        # Choose category based on emotion and intensity
        if emotion in ['sad', 'frustrated', 'angry']:
            category = 'empathy_strong' if intensity == 'high' else 'empathy_light'
        elif emotion in ['excited', 'happy']:
            category = 'strong_agreement' if intensity == 'high' else 'light_agreement'
        elif emotion == 'confused':
            category = 'encouragement'
        else:
            category = random.choice(['light_agreement', 'curiosity', 'processing'])
        
        # Get options and avoid recent ones
        options = self.advanced_backchannels[lang].get(category, ['okay'])
        available_options = [opt for opt in options if opt not in self.recent_backchannels]
        
        if not available_options:
            available_options = options  # Reset if all used
            self.recent_backchannels = []
        
        chosen = random.choice(available_options)
        self._track_usage(chosen, self.recent_backchannels)
        
        return chosen
    
    def get_sophisticated_repair(self, confidence: float, lang: str) -> str:
        """Get intelligent repair based on confidence level."""
        if confidence > 0.5:
            category = 'confident_partial'
        elif confidence > 0.2:
            category = 'gentle_clarification'
        else:
            category = 'graceful_confusion'
        
        options = self.sophisticated_repairs[lang].get(category, ['Could you repeat that?'])
        available_options = [opt for opt in options if opt not in self.recent_repairs]
        
        if not available_options:
            available_options = options
            self.recent_repairs = []
        
        chosen = random.choice(available_options)
        self._track_usage(chosen, self.recent_repairs)
        
        return chosen
    
    def _should_backchannel(self) -> bool:
        """Decide if backchannel is appropriate based on personality."""
        base_probability = 0.25
        
        # Adjust based on personality traits
        probability = base_probability * self.personality.traits['empathy_sensitivity']
        probability *= (1 + self.personality.traits['warmth'] - 0.5)
        
        # Reduce if conversation is becoming too choppy
        if len(self.recent_backchannels) > 2:
            probability *= 0.6
            
        return random.random() < probability
    
    def _track_usage(self, item: str, tracker: List[str]):
        """Track recently used items to prevent repetition."""
        tracker.append(item)
        if len(tracker) > self.max_recent_items:
            tracker.pop(0)

class AdvancedEmotionDetector:
    """Enhanced emotion detection with intensity and context."""
    
    def __init__(self):
        self.emotion_patterns = {
            'happy': {
                'keywords': ['good', 'great', 'awesome', 'wonderful', 'excellent', 'amazing', 'fantastic', 'love', 'excited'],
                'intensifiers': ['super', 'really', 'absolutely', 'totally', 'incredibly', 'extremely']
            },
            'sad': {
                'keywords': ['sad', 'terrible', 'awful', 'horrible', 'disappointed', 'upset', 'depressed', 'down'],
                'intensifiers': ['really', 'very', 'extremely', 'totally', 'completely']
            },
            'frustrated': {
                'keywords': ['frustrated', 'annoying', 'irritating', 'angry', 'mad', 'furious'],
                'intensifiers': ['so', 'really', 'extremely', 'very', 'totally']
            },
            'confused': {
                'keywords': ['confused', 'don\'t understand', 'unclear', 'what', 'how', 'why', 'huh'],
                'intensifiers': ['really', 'completely', 'totally', 'very']
            },
            'excited': {
                'keywords': ['excited', 'thrilled', 'can\'t wait', 'amazing', 'incredible', 'wow'],
                'intensifiers': ['super', 'really', 'so', 'extremely', 'absolutely']
            }
        }
    
    def detect_emotion_with_intensity(self, text: str) -> Tuple[str, str]:
        """Detect emotion and intensity level."""
        text_lower = text.lower()
        detected_emotion = 'neutral'
        intensity = 'low'
        
        for emotion, patterns in self.emotion_patterns.items():
            if any(keyword in text_lower for keyword in patterns['keywords']):
                detected_emotion = emotion
                
                # Check for intensifiers
                if any(intensifier in text_lower for intensifier in patterns['intensifiers']):
                    intensity = 'high'
                elif len([k for k in patterns['keywords'] if k in text_lower]) > 1:
                    intensity = 'medium'
                else:
                    intensity = 'low'
                break
        
        return detected_emotion, intensity

class AdvancedNoiseFilter:
    """Enhanced noise filtering with adaptive learning."""
    
    def __init__(self):
        # Optimized thresholds for sophisticated conversation
        self.confidence_threshold = 0.6
        self.min_word_length = 2
        self.min_total_length = 2  # More permissive
        self.max_repetition_ratio = 0.6
        
        # Enhanced patterns
        self.noise_patterns = [
            re.compile(r'^[0-9]{3,} '),
            re.compile(r'^[0]{2,} '),
            re.compile(r'^[1]{2,} '),
            re.compile(r'^[2-9]{2,} '),
            re.compile(r'^[\.\,\!\?\-]{2,} '),
            re.compile(r'^[a-z] '),
            re.compile(r'^\W+ '),
            re.compile(r'^(hm+|um+|uh+|ah+|oh+) ', re.IGNORECASE),
            re.compile(r'^[a-z]{1,2} '),
            re.compile(r'^(na|da|ba|ka|ta|pa|ma|la|ra|sa|ha|ja|wa|ya){2,} ', re.IGNORECASE),
            re.compile(r'^(.)\1{2,} '),  # Repeated characters
            re.compile(r'^[bcdfghjklmnpqrstvwxyz]+ ', re.IGNORECASE),  # Only consonants
            re.compile(r'^[aeiou]+ ', re.IGNORECASE),  # Only vowels
        ]
        
        self.definite_noise_patterns = [
            re.compile(r'^[0-9]{4,} '),
            re.compile(r'^[0]{3,} '),
            re.compile(r'^[1]{3,} '),
            re.compile(r'^(.)\1{3,} '),
            re.compile(r'^[\d\W]+ '),
        ]
        
        # Allow some natural conversation fillers
        self.noise_words = {
            'zero', 'zeros', 'one', 'ones', 'two', 'twos', 'three', 'threes',
            'four', 'fours', 'five', 'fives', 'six', 'sixes', 'seven', 'sevens',
            'eight', 'eights', 'nine', 'nines', 'ten', 'tens',
            'beep', 'boop', 'buzz', 'static', 'noise', 'sound'
            # Removed 'um', 'uh' etc. as these can be natural human speech
        }
        
        # Learning metrics
        self.validation_cache = {}
        self.cache_size = 500
        self.total_inputs = 0
        self.rejected_inputs = 0
        
    def is_valid_speech(self, text: str, confidence: float = 0.0) -> bool:
        """Enhanced speech validation allowing natural human speech patterns."""
        self.total_inputs += 1
        
        if not text or not text.strip():
            return False
            
        text_clean = text.strip().lower()
        
        # Cache check
        cache_key = f"{text_clean}_{confidence:.2f}"
        if cache_key in self.validation_cache:
            result = self.validation_cache[cache_key]
            if not result:
                self.rejected_inputs += 1
            return result
        
        # More permissive confidence threshold
        if confidence > 0 and confidence < self.confidence_threshold:
            logger.debug(f"Rejected due to low confidence: '{text}' (confidence: {confidence:.2f})")
            self._cache_result(cache_key, False)
            return False
        
        # Allow shorter inputs for natural conversation
        if len(text_clean) < self.min_total_length:
            # But allow some common short responses
            if text_clean in ['ok', 'no', 'yes', 'hi', 'hey', 'bye', 'wow', 'oh', 'ah', 'mm']:
                self._cache_result(cache_key, True)
                return True
            logger.debug(f"Rejected due to short length: '{text}' (length: {len(text_clean)})")
            self._cache_result(cache_key, False)
            return False
        
        # Definite noise patterns
        for pattern in self.definite_noise_patterns:
            if pattern.match(text_clean):
                logger.debug(f"Rejected as definite noise: '{text}' (pattern match)")
                self._cache_result(cache_key, False)
                return False
        
        # Enhanced word validation
        words = text_clean.split()
        if len(words) == 1 and words[0] in self.noise_words:
            logger.debug(f"Rejected as noise word: '{text}'")
            self._cache_result(cache_key, False)
            return False
        
        # Character analysis (more permissive)
        if len(text_clean) > 3:
            char_counts = {}
            for char in text_clean.replace(' ', ''):
                char_counts[char] = char_counts.get(char, 0) + 1
            
            if char_counts:
                max_repetition = max(char_counts.values())
                repetition_ratio = max_repetition / len(text_clean.replace(' ', ''))
                
                if repetition_ratio > self.max_repetition_ratio:
                    logger.debug(f"Rejected due to high repetition: '{text}' (ratio: {repetition_ratio:.2f})")
                    self._cache_result(cache_key, False)
                    return False
        
        # Reduced pattern matching sensitivity
        suspicious_count = 0
        for pattern in self.noise_patterns:
            if pattern.match(text_clean):
                suspicious_count += 1
        
        # Only reject if multiple suspicious patterns match
        if suspicious_count > 1:
            logger.debug(f"Rejected as suspicious: '{text}' (matches {suspicious_count} patterns)")
            self._cache_result(cache_key, False)
            return False
        
        # Must contain alphabetic characters (more permissive)
        if not any(c.isalpha() for c in text_clean) and len(text_clean) > 2:
            logger.debug(f"Rejected - no alphabetic characters: '{text}'")
            self._cache_result(cache_key, False)
            return False
        
        # If we get here, it's valid
        logger.debug(f"Accepted as valid speech: '{text}'")
        self._cache_result(cache_key, True)
        return True
    
    def _cache_result(self, cache_key: str, result: bool):
        """Cache validation result with size limit."""
        if len(self.validation_cache) >= self.cache_size:
            # Remove oldest entries (simple FIFO)
            oldest_key = next(iter(self.validation_cache))
            del self.validation_cache[oldest_key]
        
        self.validation_cache[cache_key] = result
    
    def get_stats(self) -> Dict[str, float]:
        """Get filter performance statistics."""
        if self.total_inputs == 0:
            return {"total_inputs": 0, "rejection_rate": 0.0, "cache_size": 0}
        
        return {
            "total_inputs": self.total_inputs,
            "rejection_rate": self.rejected_inputs / self.total_inputs,
            "cache_size": len(self.validation_cache)
        }

# =============================================
# END SOPHISTICATED PERSONALITY COMPONENTS
# =============================================

class P2PLendingFunctions:
    """Enhanced P2P lending functions with zero-latency optimization and knowledge base focus."""
    
    def __init__(self, vector_db_client: VectorDBClient):
        self.vector_db_client = vector_db_client
        
        # Load config for max_results
        import yaml
        try:
            with open("vectordb/vectordb_config.yml", 'r') as f:
                config = yaml.safe_load(f)
                self.default_max_results = config.get('retriever_defaults', {}).get('similarity', {}).get('k', 5)
        except Exception as e:
            logger.warning(f"Could not load config, using default max_results=5: {e}")
            self.default_max_results = 5
        
        self.collection_name = 'faqs_p2p'
        
        # Initialize vector store
        try:
            self.vector_db_client.load_existing_collections([self.collection_name])
            logger.info("✅ Vector database loaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to load vector database: {e}")

    def search_p2p_knowledge(self, user_query: str, max_results: int = None) -> List[Dict[str, Any]]:
        """
        Search the P2P lending knowledge base for relevant information.
        PRIMARY SOURCE OF TRUTH for all P2P queries - no hardcoded responses.
        
        Args:
            user_query: The user's question about P2P lending
            max_results: Maximum number of results to return (uses config default if None)
            
        Returns:
            List of relevant documents with content and metadata
        """
        try:
            start_time = time.time()
            
            # Use default from config if max_results not specified
            if max_results is None:
                default_k = self.vector_db_client._config.get('retriever_defaults', {}).get('similarity', {}).get('k', 3)
                max_results = default_k
            
            documents = self.vector_db_client.query_vectordb(
                collection_name=self.collection_name,
                user_query=user_query,
                retriever_type="similarity"
            )
            
            # Format results for function calling
            results = []
            for doc in documents[:max_results]:
                results.append({
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "relevance_score": "high"
                })
            
            search_time = time.time() - start_time
            logger.info(f"🔍 Vector search completed in {search_time:.2f}s, found {len(results)} results")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Error in vector search: {e}")
            return [{"content": "I'm having trouble accessing the knowledge base right now. Please try again.", "error": str(e)}]
    
    def check_query_relevance(self, user_query: str, detected_language: str = "English") -> Dict[str, Any]:
        """
        FAST relevance detection using keyword matching (no LLM calls).
        This handles multiple languages efficiently without latency.
        
        Args:
            user_query: The user's input in any language
            detected_language: Language detected (optional)
            
        Returns:
            Dictionary with relevance information
        """
        # P2P lending keywords in multiple languages
        p2p_keywords = {
            'english': [
                'lend', 'lending', 'loan', 'borrow', 'borrowing', 'interest', 'investment',
                'peer to peer', 'p2p', 'credit', 'returns', 'risk', 'platform',
                'lendenclub', 'lenden club', 'portfolio', 'diversification', 'default',
                'emi', 'tenure', 'principal', 'maturity', 'disbursal', 'approval',
                'kyc', 'verification', 'documents', 'eligibility', 'income', 'salary',
                'cibil', 'credit score', 'rating', 'assessment', 'collateral', 'app', 'application', 
                'lendenclub', 'lenden'
            ],
            'hindi': [
                'उधार', 'लेंडिंग', 'लोन', 'उधार लेना', 'ब्याज', 'निवेश', 'प्लेटफॉर्म',
                'पैसा', 'पैसे', 'रुपये', 'क्रेडिट', 'रिटर्न', 'रिस्क', 'जोखिम',
                'आय', 'वेतन', 'दस्तावेज़', 'योग्यता', 'केवाईसी', 'अप्रूवल'
            ],
            'hinglish': [
                'paisa', 'paise', 'rupees', 'rs', 'udhaar', 'lagane', 'nivesh', 
                'faayda', 'munafa', 'nuksan', 'khatre', 'sudh', 'byaaj', 'invest',
                'karna', 'hai', 'hota', 'safe', 'secure', 'documents', 'chahiye', 'kyc', 'ckyc', 
                'nuskaan', 'fayda'
            ]
        }
        
        # Financial context words
        financial_words = [
            'money', 'financial', 'finance', 'payment', 'amount', 'bank', 'account', 
            'transaction', 'rate', 'percentage', '%', 'profit', 'loss', 'earn', 'earning'
        ]
        
        query_lower = user_query.lower()
        
        # Check for matches across all languages
        all_keywords = []
        for lang_keywords in p2p_keywords.values():
            all_keywords.extend(lang_keywords)
        
        direct_matches = [keyword for keyword in all_keywords if keyword in query_lower]
        financial_context = any(word in query_lower for word in financial_words)
        
        # Enhanced relevance scoring
        relevance_score = len(direct_matches) * 2 + (1 if financial_context else 0)
        
        # Additional context checks
        question_indicators = any(word in query_lower for word in [
            'what', 'how', 'why', 'when', 'where', 'which', 'can', 'should', 'is', 'are',
            'kya', 'kaise', 'kyun', 'kab', 'kahan', 'kaun', 'hota', 'hai', 'sakta'
        ])
        
        if question_indicators and (direct_matches or financial_context):
            relevance_score += 1
            
        is_relevant = relevance_score > 0 or len(direct_matches) > 0
        
        # Determine confidence
        if relevance_score >= 4:
            confidence = "high"
        elif relevance_score >= 2:
            confidence = "medium"
        elif relevance_score >= 1:
            confidence = "low"
        else:
            confidence = "none"
        
        return {
            "is_relevant": is_relevant,
            "relevance_score": relevance_score,
            "matched_keywords": direct_matches,
            "has_financial_context": financial_context,
            "user_language": detected_language,
            "confidence": confidence,
            "question_type": "inquiry" if question_indicators else "statement"
        }

class NeuraVoiceV2(Agent):
    """
    FIXED: NeuraVoice V2 inheriting from Agent like witty_voice_bot.py
    This ensures proper integration of instructions and Gemini LLM together.
    """
    
    def __init__(self): #  ctx: JobContext
        #self.ctx = ctx
        # Initialize all personality components
        self.personality = DynamicPersonality()
        self.micro_interactions = SophisticatedMicroInteractions(self.personality)
        self.emotion_detector = AdvancedEmotionDetector()
        self.noise_filter = AdvancedNoiseFilter()
        
        # Initialize conversation memory and performance metrics
        self.conversation_history = []
        self.conversation_memory = {
            'exchanges': [],
            'topics': set(),
            'rapport_level': 0.0,
            'personality_adjustments': 0,
            'emotional_context': {'current': 'neutral', 'intensity': 'low'}
        }
        
        self.performance_metrics = {
            'total_queries': 0,
            'function_calls': 0,
            'vector_searches': 0,
            'avg_response_time': 0.0,
            'backchannels_used': 0,
            'personality_adjustments': 0
        }
        
        # 📊 COMPREHENSIVE METRICS COLLECTION (like neuravoice_v2.py)
        self.usage_collector = metrics.UsageCollector()
        self.query_metrics = []  # Store per-query metrics
        self.current_query_start_time = None
        self.current_query_text = None
        
        # Validate environment and initialize components
        self._validate_environment()
        
        # Initialize vector DB and P2P functions
        self.vector_db_client = VectorDBClient(
            db_path=VECTOR_DB_PATH,
            config_path=VECTOR_CONFIG_PATH
        )
        self.p2p_functions = P2PLendingFunctions(self.vector_db_client)
        
        # Create enhanced instructions first before super().__init__

        # All critical behaviors now consolidated in _create_corrected_instructions
        enhanced_instructions = self._create_corrected_instructions()
        
        # Available Gemini voices
        AVAILABLE_VOICES = {
            'male': ['Puck', 'Charon', 'Fenrir', 'Orus', 'Enceladus', 'Iapetus', 'Umbriel'],
            'female': ['Aoede', 'Kore', 'Leda', 'Zephyr', 'Autonoe', 'Callirrhoe', 'Despina', 'Erinome', 'Sadachbia']
        }
        
        # Choose a voice (you can change this to any voice from AVAILABLE_VOICES)
        selected_voice = 'Kore'  # Female voice with natural tone
        
        # Validate selected voice
        all_voices = AVAILABLE_VOICES['male'] + AVAILABLE_VOICES['female']
        if selected_voice not in all_voices:
            logger.warning(f"Invalid voice '{selected_voice}'. Defaulting to 'Kore'")
            selected_voice = 'Kore'
        
        logger.info(f"Using voice: {selected_voice}")
        
        super().__init__(
            instructions=enhanced_instructions,
            
            # Use Gemini's RealtimeModel with Vertex AI
            #llm=google.beta.realtime.RealtimeModel(
            llm=RealtimeModel(
                model="gemini-2.5-flash-preview-native-audio-dialog",
                #model="gemini-2.0-flash-exp",
                voice=selected_voice,
                temperature=0.7,
                #instructions=enhanced_instructions,
                #vertexai=True,
                project=os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-project-459106'),
                location='us-central1'
            ),
             
            # Enhanced interrupt handling
            allow_interruptions=True
        )
        
        logger.info(" NeuraVoice V2 initialized successfully with sophisticated personality!")

    async def on_enter(self):
        """Called when agent enters the session - provide greeting with robust error handling for LiveKit metrics bug."""
        import random
        
        # Simple working greeting instructions
        greeting_variations = [
            "Mention who you are and greet the user in short sentence",
            "Mention who you are and greet the user and talk about the purpose of the call in short sentence",
            "Introduce yourself briefly as Priya from LendenClub and greet the user in English",
            "Greet the user warmly, mention you're Priya from LendenClub calling about P2P lending"
        ]
        
        # Randomly select one of the greeting variations
        selected_greeting = random.choice(greeting_variations)
        logger.info(" [ON_ENTER] Attempting to generate initial greeting")
        logger.info(f" [ON_ENTER] Selected greeting instruction: {selected_greeting}")
        
        # Enhanced error handling for the known LiveKit metrics bug (GitHub issue #2349)
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.session.generate_reply(instructions=selected_greeting)
                logger.info(" [ON_ENTER] Greeting generation initiated successfully")
                # TIMING OPTIMIZATION: Give audio pipeline time to process
                logger.info(" [ON_ENTER] Waiting for audio processing to stabilize...")
                await asyncio.sleep(5.0)  # Allow audio pipeline to process without metrics conflicts
                
                return  # Success, exit the retry loop
                
            except ZeroDivisionError as e:
                logger.warning(f" [ON_ENTER] LiveKit metrics bug encountered (attempt {attempt + 1}/{max_retries}): {e}")
                logger.warning(" [ON_ENTER] This is a known issue in LiveKit Agents 1.0.21+ (GitHub #2349)")
                if attempt < max_retries - 1:
                    await asyncio.sleep(0.5)  # Brief delay before retry
                    continue
                else:
                    logger.error(" [ON_ENTER] All greeting attempts failed due to metrics bug")
                    
            except TypeError as e:
                if "NoneType" in str(e):
                    logger.warning(f" [ON_ENTER] LiveKit token count bug encountered (attempt {attempt + 1}/{max_retries}): {e}")
                    logger.warning(" [ON_ENTER] This is a known issue in LiveKit Agents 1.0.21+ (GitHub #2349)")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(0.5)  # Brief delay before retry
                        continue
                    else:
                        logger.error(" [ON_ENTER] All greeting attempts failed due to token count bug")
                else:
                    logger.error(f" [ON_ENTER] Unexpected TypeError in greeting generation: {e}")
                    break
                    
            except Exception as e:
                logger.error(f" [ON_ENTER] Unexpected error in greeting generation: {e}")
                # Try fallback greeting once
                try:
                    logger.info(" [ON_ENTER] Trying fallback greeting...")
                    self.session.generate_reply(instructions="Greet the user briefly")
                    logger.info(" [ON_ENTER] Fallback greeting initiated")
                    return
                except Exception as e2:
                    logger.error(f" [ON_ENTER] Fallback greeting also failed: {e2}")
                    break
        
        logger.info(" [ON_ENTER] Agent ready for user input (greeting generation failed due to LiveKit bugs)")
        logger.info(" [ON_ENTER] Tip: Consider downgrading to LiveKit Agents 1.0.20 to avoid these issues")


    async def on_user_speech_committed(self, user_speech: str):
        """Called when user speech is committed - start metrics tracking."""
        logger.info(f" [USER SPEECH COMMITTED] Starting metrics for: '{user_speech[:50]}...'")
        self.start_query_metrics(user_speech)
        
        # Call parent method if it exists
        if hasattr(super(), 'on_user_speech_committed'):
            await super().on_user_speech_committed(user_speech)

    async def on_agent_speech_committed(self, agent_speech: str):
        """Called when agent speech is committed - end metrics tracking and update conversation memory."""
        logger.info(f" [AGENT SPEECH COMMITTED] Ending metrics for: '{agent_speech[:50]}...'")
        self.end_query_metrics(agent_speech)
        
        # Call parent method if it exists
        if hasattr(super(), 'on_agent_speech_committed'):
            await super().on_agent_speech_committed(agent_speech)

    def log_startup_status(self):
        """Log comprehensive startup status."""
        logger.info("="*80)
        logger.info(" [NEURAVOICE V2 STARTUP] System initialization complete!")
        logger.info("="*80)
        logger.info(" [PERSONALITY] Dynamic personality traits active")
        logger.info(" [MICRO-INTERACTIONS] Sophisticated micro-interactions enabled")
        logger.info(" [EMOTION DETECTION] Advanced emotion detection with intensity tracking")
        logger.info(" [NOISE FILTERING] Enhanced noise filtering active")
        logger.info(" [VECTOR DB] Knowledge base loaded and tested")
        logger.info(" [LLM] Using Gemini RealtimeModel with enhanced instructions")
        logger.info(" [LANGUAGE] Multi-language support: English, Hindi, Hinglish")
        logger.info("="*80)
        
        # Display performance metrics
        stats = self.get_performance_stats()
        logger.info(f" [METRICS] Queries: {stats['total_queries']} | Functions: {stats['function_calls']} | Vector searches: {stats['vector_searches']}")
        
        # Display personality traits
        if hasattr(self, 'personality') and self.personality:
            traits = self.personality.traits
            logger.info(f" [PERSONALITY TRAITS] Wit: {traits.get('wit_level', 0):.1f}/1.0 | Curiosity: {traits.get('curiosity', 0):.1f}/1.0 | Warmth: {traits.get('warmth', 0):.1f}/1.0")
            
        logger.info("="*80)
        logger.info(" [SYSTEM READY] NeuraVoice V2 is FULLY OPERATIONAL and ready for sophisticated conversations!")
        logger.info("="*80)

    def _validate_environment(self):
        """Validate required environment variables."""
        required_vars = {
            'GOOGLE_APPLICATION_CREDENTIALS': os.getenv('GOOGLE_APPLICATION_CREDENTIALS'),
            'GOOGLE_CLOUD_PROJECT': os.getenv('GOOGLE_CLOUD_PROJECT')
        }
        
        # Set default project if not provided
        if not required_vars['GOOGLE_CLOUD_PROJECT']:
            os.environ['GOOGLE_CLOUD_PROJECT'] = 'ai-project-459106'
            required_vars['GOOGLE_CLOUD_PROJECT'] = 'ai-project-459106'
        
        missing_vars = [var for var, value in required_vars.items() if not value]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        logger.info(" Environment variables validated")
    
    def _create_corrected_instructions(self) -> str:
        """Create OPTIMIZED instructions with minimal function calling latency."""
        # Use unified system prompt that handles both P2P and out-of-context queries
        unified_system_prompt = get_unified_sys_prompt()
        
        # Add personality-aware instructions
        personality_injection = ""
        if hasattr(self, 'personality'):
            personality_injection = self.personality.get_personality_prompt_injection()
        
        # OPTIMIZED instructions for low-latency function calling
        enhanced_instructions = f"""        
        {personality_injection}

        **PERSONALITY TRAITS (Optimized):**
        - Wit Level: {self.personality.traits['wit_level']:.1f}/1.0
        - Curiosity: {self.personality.traits['curiosity']:.1f}/1.0  
        - Warmth: {self.personality.traits['warmth']:.1f}/1.0

        """
        
        return unified_system_prompt + enhanced_instructions
    
    def _test_vector_db(self):
        """Test vector database connectivity and functionality with comprehensive logging."""
        try:
            logger.info("="*80)
            logger.info("🧪 [VECTOR DB TEST STARTED] Testing vector database connectivity...")
            logger.info("="*80)
            
            start_time = time.time()
            
            # Test with a simple P2P query
            test_query = "What is P2P lending?"
            logger.info(f"🧪 [VECTOR DB TEST] Running test query: '{test_query}'")
            
            test_results = self.p2p_functions.search_p2p_knowledge(test_query, max_results=2)
            
            end_time = time.time()
            search_duration = end_time - start_time
            
            logger.info(f"🧪 [VECTOR DB TEST] Search completed in {search_duration:.3f} seconds")
            logger.info(f"🧪 [VECTOR DB TEST] Results count: {len(test_results)}")
            
            if test_results:
                logger.info("📋 [VECTOR DB TEST] Sample results found:")
                for i, result in enumerate(test_results):
                    content_preview = result.get('content', '')[:150] + "..." if len(result.get('content', '')) > 150 else result.get('content', '')
                    source_info = result.get('metadata', {}).get('source', 'Unknown')
                    logger.info(f"   📄 Result {i+1} (Source: {source_info}): {content_preview}")
                
                logger.info("="*80)
                logger.info("✅ [VECTOR DB TEST SUCCESS] Vector database is FULLY OPERATIONAL!")
                logger.info(f"✅ [VECTOR DB TEST SUCCESS] Ready to serve P2P lending queries with {search_duration:.3f}s avg response time")
                logger.info("="*80)
            else:
                logger.warning("="*80)
                logger.warning("⚠️ [VECTOR DB TEST WARNING] Vector database returned no results!")
                logger.warning("⚠️ [VECTOR DB TEST WARNING] Check if knowledge base is properly loaded")
                logger.warning("="*80)
                
        except Exception as e:
            logger.error("="*80)
            logger.error(f"❌ [VECTOR DB TEST FAILED] Vector database test failed: {str(e)}")
            logger.error(f"❌ [VECTOR DB TEST FAILED] This will impact P2P question answering capability")
            logger.error(f"❌ [VECTOR DB TEST FAILED] Traceback: {traceback.format_exc()}")
            logger.error("="*80)

    
    @function_tool
    async def search_knowledge_base(self, context: RunContext, user_query: str, max_results: int = 3):
        """Search P2P lending knowledge base - optimized for speed.
        
        Args:
            context: RunContext from LiveKit
            user_query: The user's P2P lending question to search for
            max_results: Maximum number of results to return (default: 3)
        """
        logger.info(f"🔍 Searching KB for: '{user_query[:50]}...'")
        
        self.performance_metrics['function_calls'] += 1
        self.performance_metrics['vector_searches'] += 1
        
        try:
            start_time = time.time()
            results = self.p2p_functions.search_p2p_knowledge(user_query, max_results)
            search_duration = time.time() - start_time
            
            logger.info(f"✅ KB search completed: {len(results)} results in {search_duration:.2f}s")
            
            if not results:
                return [{"content": "I don't have specific information about that. Could you rephrase your question?", "source": "fallback"}]
            
            # Simplified result formatting
            formatted_results = []
            for i, result in enumerate(results, 1):
                formatted_results.append({
                    "content": result.get('content', ''),
                    "source": result.get('metadata', {}).get('source', 'Knowledge Base'),
                    "result_number": i
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"❌ KB search error: {e}")
            return [{"content": f"Search error: {str(e)}", "source": "error"}]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        return {
            'total_queries': self.performance_metrics['total_queries'],
            'function_calls': self.performance_metrics['function_calls'],
            'vector_searches': self.performance_metrics['vector_searches'],
            'avg_response_time': self.performance_metrics['avg_response_time'],
            'personality_traits': self.personality.traits if hasattr(self, 'personality') else {},
            'rapport_level': self.conversation_memory.get('rapport_level', 0.0) if hasattr(self, 'conversation_memory') else 0.0,
            'backchannels_used': self.performance_metrics.get('backchannels_used', 0),
            'personality_adjustments': self.performance_metrics.get('personality_adjustments', 0),
        }

    def start_query_metrics(self, user_input: str):
        """Start tracking metrics for a new user query."""
        self.current_query_start_time = time.time()
        self.current_query_text = user_input
        logger.info(f"📊 [QUERY METRICS START] Query: '{user_input[:50]}...' | Start time: {self.current_query_start_time}")

    def end_query_metrics(self, assistant_response: str):
        """End tracking metrics for the current query and log comprehensive stats."""
        if self.current_query_start_time is None:
            return
        
        end_time = time.time()
        total_time = end_time - self.current_query_start_time
        
        # Get latest usage summary
        usage_summary = self.usage_collector.get_summary()
        
        # Create comprehensive query metrics
        query_metric = {
            'timestamp': datetime.now().isoformat(),
            'user_query': self.current_query_text,
            'assistant_response': assistant_response,
            'total_time_seconds': total_time,
            'usage_summary': usage_summary,
            'query_number': len(self.query_metrics) + 1
        }
        
        self.query_metrics.append(query_metric)
        
        # 📊 COMPREHENSIVE METRICS LOGGING
        logger.info("="*120)
        logger.info(f"📊 [QUERY METRICS COMPLETE] Query #{query_metric['query_number']}")
        logger.info("="*120)
        logger.info(f"👤 [USER INPUT]: {self.current_query_text}")
        logger.info(f"🤖 [ASSISTANT RESPONSE]: {assistant_response}")
        logger.info(f"⏱️ [TOTAL TIME]: {total_time:.3f} seconds")
        
        # Log detailed usage metrics
        if usage_summary:
            logger.info("📈 [USAGE METRICS]:")
            for service, metrics in usage_summary.items():
                logger.info(f"  🔧 {service.upper()}:")
                if isinstance(metrics, dict):
                    for metric_name, value in metrics.items():
                        if isinstance(value, (int, float)):
                            logger.info(f"    • {metric_name}: {value}")
                        else:
                            logger.info(f"    • {metric_name}: {value}")
                else:
                    logger.info(f"    • Total: {metrics}")
        
        # Log TTS text that will be spoken
        logger.info("="*120)
        logger.info("🔊 [TTS OUTPUT] Text that will be spoken:")
        logger.info(f"🗣️ \"{assistant_response}\"")
        logger.info("="*120)
        
        # Reset for next query
        self.current_query_start_time = None
        self.current_query_text = None

    def on_metrics_collected(self, event: MetricsCollectedEvent):
        """Handle metrics collection events (like neuravoice_v2.py)."""
        # Log individual metrics as they come in
        logger.info(f"📊 [METRICS COLLECTED] {event.metrics}")
        
        # Collect for summary
        self.usage_collector.collect(event.metrics)
        
        # Extract key metrics for detailed logging
        for metric in event.metrics:
            metric_type = type(metric).__name__
            
            if hasattr(metric, 'ttfb') and metric.ttfb is not None:
                logger.info(f"⚡ [TTFB] {metric_type}: {metric.ttfb:.3f}s (Time to First Byte)")
            
            if hasattr(metric, 'duration') and metric.duration is not None:
                logger.info(f"⏱️ [DURATION] {metric_type}: {metric.duration:.3f}s")
            
            if hasattr(metric, 'characters_count') and metric.characters_count is not None:
                logger.info(f"📝 [CHARACTERS] {metric_type}: {metric.characters_count}")
            
            if hasattr(metric, 'tokens_count') and metric.tokens_count is not None:
                logger.info(f"🔢 [TOKENS] {metric_type}: {metric.tokens_count}")
            
            if hasattr(metric, 'audio_duration') and metric.audio_duration is not None:
                logger.info(f"🎵 [AUDIO DURATION] {metric_type}: {metric.audio_duration:.3f}s")

    def log_final_usage_summary(self):
        """Log final usage summary (called on shutdown)."""
        summary = self.usage_collector.get_summary()
        logger.info("="*120)
        logger.info("📊 [FINAL USAGE SUMMARY]")
        logger.info("="*120)
        logger.info(f"📈 Total Queries Processed: {len(self.query_metrics)}")
        
        if summary:
            for service, metrics in summary.items():
                logger.info(f"🔧 {service.upper()} USAGE:")
                if isinstance(metrics, dict):
                    for metric_name, value in metrics.items():
                        logger.info(f"  • {metric_name}: {value}")
                else:
                    logger.info(f"  • Total: {metrics}")
        
        # Log average response time
        if self.query_metrics:
            avg_time = sum(q['total_time_seconds'] for q in self.query_metrics) / len(self.query_metrics)
            logger.info(f"⏱️ Average Response Time: {avg_time:.3f}s")
        
        logger.info("="*120)
        
    # Dictionary to store pending responses for portfolio analysis
    _portfolio_analysis_tasks = {}
    
        
    # @function_tool
    # async def analyze_portfolio(self, context: RunContext, user_query: str):
    #     """Verify user by matching their full name and account last 4 digits for security verification"""
        
    #     logger.info("🎯" * 60)
    #     logger.info("🔧 [Analyze Portfolio FUNCTION CALLED] FUNCTION CALLING IS WORKING!")
    #     logger.info(f"🔧 [CONTEXT FUNCTION] Searching for user question: '{user_query}'")
    #     logger.info("🎯" * 60)
        
    #     # Get the knowledge graph documents
    #     doc_processor = DocumentProcessor()

    #     # Set your temperature and other parameters here (now centralized in LLM client)
    #     doc_processor.set_llm_parameters(
    #         temperature=0.1,  # Change this to your preferred temperature
    #         max_tokens=2000,
    #         top_p=1.0
    #     )

    #     # Get information about the hardcoded file paths
    #     file_info = doc_processor.get_hardcoded_file_paths()
    #     print(f"Configured files: {file_info}")

    #     # Check if files were processed successfully
    #     if not doc_processor.processed_files:
    #         print("\n❌ No files were successfully processed!")
    #         print("Please check that your hardcoded files exist in the data/ folder.")
    #         return "We don't have enough information regarding this user query, request you to contact support"
    #     else:
    #         # Get information about processed files
    #         info = doc_processor.get_processed_files_info()
    #         print(f"Processed {info['files_count']} files, total content length: {info['content_length']} characters")
            
    #         # Ask a question about the processed documents
    #         answer = doc_processor.answer_question(user_query)
            
    #     if answer:
    #         return answer
    #     else:
    #         return "We don't have enough information regarding this user query, request you to contact support"


    # @function_tool
    # async def verify_user(self, context: RunContext, userfullname: str, last4_digits: str):
    #     """Verify user by matching their full name and account last 4 digits for security verification"""
        
    #     logger.info("🎯" * 60)
    #     logger.info("🔧 [VERIFY USER FUNCTION CALLED] FUNCTION CALLING IS WORKING!")
    #     logger.info(f"🔧 [CONTEXT FUNCTION] Searching for Fullname: '{userfullname}'")
    #     logger.info(f"🔧 [CONTEXT FUNCTION] Searching for Last4digits: '{last4_digits}'")
        
    #     logger.info("🎯" * 60)
        
    #     authentication = LendingAuthManager()
    #     status, session_id = authentication.authenticate_user(userfullname, last4_digits)
    #     if status:
    #         return "Thank you for verifying your details, how can i help you?"
    #     else:
    #         return "Account details not verified, kindly share correct credentials"
            
    # @function_tool
    # async def send_otp(self, context: RunContext, userfullname: str, mobilenumber: str):
    #     """Generate and send one-time-password OTP user by matching their mobile number, 
    #     If verification is successful then return status code and otp for validaiton
    #     If verification is failed, the return status code and null opt"""
        
    #     logger.info("🎯" * 60)
    #     logger.info("🔧 [SEND OTP FUNCTION CALLED] FUNCTION CALLING IS WORKING!")
    #     logger.info(f"🔧 [CONTEXT FUNCTION] Searching for Fullname: '{userfullname}'")
    #     logger.info(f"🔧 [CONTEXT FUNCTION] Searching for Mobilenumber: '{mobilenumber}'")
        
    #     logger.info("🎯" * 60)
        
    #     url = "https://api.authkey.io/request"
    #     params = {
    #         "authkey": "fdef84cdcd1bf23f",
    #         "mobile": mobilenumber.strip(),
    #         "country_code": "+91",
    #         "sid": "24784",
    #         "name": userfullname,
    #         "otp": "9999"
    #     }

    #     response = requests.get(url, params=params)

    #     print("Status Code:", response.status_code)
    #     print("Response Body:", response.text)

    #     if response.status_code==200:
    #         status_message = "Can you please check and confirm the OTP"
    #         otp = "9999"
    #         return [status_message, otp]
    #     else:
    #         status_message = "Mobile number not verified, kindly recheck"
    #         otp = ""
    #         return [status_message, otp]

async def entrypoint(ctx: JobContext):
    """FIXED: Following lendbot.py pattern - proper session configuration like working code."""
    logger.info("🚀 Starting NeuraVoice V2 following lendbot.py working pattern...")
    
    try:
        # Connect to LiveKit server
        await ctx.connect()
        logger.info("✅ Connected to LiveKit server")
        selected_voice = 'Kore'

        # Create NeuraVoice V2 bot instance 
        bot = NeuraVoiceV2() # ctx
        logger.info("✅ NeuraVoice V2 created")

        # Create AgentSession with LLM model like livekit_moha.py
        #session = AgentSession(llm=llm_model)
        session = AgentSession()

        logger.info("✅ Created AgentSession with RealtimeModel for function calling")

        # 📊 SET UP COMPREHENSIVE METRICS COLLECTION (like neuravoice_v2.py)
        @session.on("metrics_collected")
        def _on_metrics_collected(event: MetricsCollectedEvent):
            """Handle metrics collection events - log and collect usage data."""
            # Log metrics as they are emitted (like neuravoice_v2.py)
            metrics.log_metrics(event.metrics)
            
            # Pass to bot for detailed processing
            bot.on_metrics_collected(event)

        # 📊 SET UP SHUTDOWN CALLBACK FOR FINAL USAGE SUMMARY (like neuravoice_v2.py)
        async def log_final_usage():
            """Log final usage summary and cleanup resources on shutdown."""
            bot.log_final_usage_summary()
            # 💾 Save persistent cache to disk
            if hasattr(bot, 'vector_db_client'):
                bot.vector_db_client.shutdown()
                logger.info("💾 Vector DB cache saved to disk")

        ctx.add_shutdown_callback(log_final_usage)
        logger.info("✅ Metrics collection and shutdown callbacks configured")
    
        # Log startup status
        bot.log_startup_status()

        # Start the session with the agent and proper room options
        await session.start(
            agent=bot,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                noise_cancellation=noise_cancellation.BVC()
            ),
            room_output_options=RoomOutputOptions(
                audio_enabled=True
            )
        )

        logger.info("✅ AgentSession started with NeuraVoice V2 bot and comprehensive metrics collection")
        
    except Exception as e:
        logger.exception(f"❌ Error in entrypoint: {str(e)}")
        raise

if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint)) 