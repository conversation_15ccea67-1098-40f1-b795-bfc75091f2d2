import os
import sys
import asyncio
import logging
import traceback
import re
import time
import json
import random
import requests
from livekit.plugins import silero
import gc  # For manual garbage collection
# psutil is optional – only used if available for accurate memory usage reporting
try:
    import psutil
except ImportError:
    psutil = None
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from contextlib import nullcontext
from livekit import rtc
from livekit.agents import cli, WorkerOptions, Agent, JobContext, AgentSession, metrics
# 👇 REQUIRED IMPORT FOR INACTIVITY HANDLING
from livekit.agents import UserStateChangedEvent
from livekit.agents.voice.room_io import RoomInputOptions, RoomOutputOptions
from livekit.agents.llm import ChatContext, ChatMessage, function_tool
from livekit.agents.voice import MetricsCollectedEvent
from livekit.agents.voice import Agent, AgentSession, RunContext
# --- ADDED FOR BACKGROUND AUDIO ---
from livekit.agents import (
    BackgroundAudioPlayer,
    AudioConfig,
    BuiltinAudioClip,
)
# --- END OF ADDED CODE ---
from livekit.plugins import (
    silero,
    google,
    noise_cancellation,
)
from clients.vector_db_client import VectorDBClient
from prompts import get_enhanced_system_prompt
from utils.performance_monitor import PerformanceMonitor, PerformanceContext
from livekit.plugins.google.beta.realtime.realtime_api import RealtimeModel
from linkup import LinkupClient

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 🔇 SUPPRESS VERBOSE DEBUG LOGS from external libraries
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)
logging.getLogger('requests').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('httpcore').setLevel(logging.WARNING)
logging.getLogger('transformers').setLevel(logging.WARNING)
logging.getLogger('huggingface_hub').setLevel(logging.WARNING)
logging.getLogger('sentence_transformers').setLevel(logging.WARNING)
logging.getLogger('torch').setLevel(logging.WARNING)
logging.getLogger('chromadb').setLevel(logging.WARNING)
logging.getLogger('openai').setLevel(logging.WARNING)
logging.getLogger('groq').setLevel(logging.WARNING)

# Keep important logs but reduce noise
logging.getLogger('livekit').setLevel(logging.INFO)
logging.getLogger('google').setLevel(logging.INFO)


# Load environment variables
load_dotenv()

# LiveKit components are already imported above
try:
    logger.info("✅ LiveKit components imported successfully")
except ImportError as e:
    logger.error(f"❌ Failed to import LiveKit components: {str(e)}")
    sys.exit(1)

# Note: With Google Realtime API, we don't need separate TTS
# The Realtime API handles voice synthesis natively

# Import existing clients
from clients.vector_db_client import VectorDBClient

# Constants
BASE_DIR = Path(__file__).resolve().parent
VECTOR_DB_PATH = BASE_DIR / "vectordb" / "bge-m3"
VECTOR_CONFIG_PATH = BASE_DIR / "vectordb" / "vectordb_config.yml"

# -------------------------------------------------------------
# Memory usage thresholds (in Megabytes)
# -------------------------------------------------------------
MEMORY_WARN_MB = int(os.getenv("NEURAVOICE_MEMORY_WARN_MB", "1500"))  # soft cleanup threshold
MEMORY_CRITICAL_MB = int(os.getenv("NEURAVOICE_MEMORY_CRITICAL_MB", "2500"))  # aggressive cleanup threshold

# =============================================
# SOPHISTICATED PERSONALITY COMPONENTS
# Integrated from witty_voice_bot.py
# =============================================



class P2PLendingFunctions:
    """Enhanced P2P lending functions with zero-latency optimization and knowledge base focus."""
    
    def __init__(self, vector_db_client: VectorDBClient):
        self.vector_db_client = vector_db_client
        
        # Load config for max_results
        import yaml
        try:
            with open("vectordb/vectordb_config.yml", 'r') as f:
                config = yaml.safe_load(f)
                self.default_max_results = config.get('retriever_defaults', {}).get('similarity', {}).get('k', 5)
        except Exception as e:
            logger.warning(f"Could not load config, using default max_results=5: {e}")
            self.default_max_results = 5
        
        self.collection_name = 'faqs_p2p'
        
        # Initialize vector store
        try:
            self.vector_db_client.load_existing_collections([self.collection_name])
            logger.info("✅ Vector database loaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to load vector database: {e}")

    def search_p2p_knowledge(self, user_query: str, max_results: int = None) -> List[Dict[str, Any]]:
        """
        Search the P2P lending knowledge base for relevant information.
        PRIMARY SOURCE OF TRUTH for all P2P queries - no hardcoded responses.
        
        Args:
            user_query: The user's question about P2P lending
            max_results: Maximum number of results to return (uses config default if None)
            
        Returns:
            List of relevant documents with content and metadata
        """
        try:
            start_time = time.time()
            
            # Use default from config if max_results not specified
            if max_results is None:
                default_k = self.vector_db_client._config.get('retriever_defaults', {}).get('similarity', {}).get('k', 3)
                max_results = default_k
            
            documents = self.vector_db_client.query_vectordb(
                collection_name=self.collection_name,
                user_query=user_query,
                retriever_type="similarity"
            )
            
            # Format results for function calling
            results = []
            for doc in documents[:max_results]:
                results.append({
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "relevance_score": "high"
                })
            
            search_time = time.time() - start_time
            logger.info(f"🔍 Vector search completed in {search_time:.2f}s, found {len(results)} results")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Error in vector search: {e}")
            return [{"content": "I'm having trouble accessing the knowledge base right now. Please try again.", "error": str(e)}]
    

class NeuraVoiceV2(Agent):
    """
    FIXED: NeuraVoice V2 inheriting from Agent like witty_voice_bot.py
    This ensures proper integration of instructions and Gemini LLM together.
    """
    
    
    def __init__(self, chat_ctx: ChatContext): #  ctx: JobContext
        # Initialize conversation memory and performance metrics
        self.performance_metrics = {
            'total_queries': 0,
            'function_calls': 0,
            'vector_searches': 0,
            'avg_response_time': 0.0
        }
        
        # 📊 COMPREHENSIVE METRICS COLLECTION (like neuravoice_v2.py)
        self.usage_collector = metrics.UsageCollector()
        self.query_metrics = []  # Store per-query metrics
        self.current_query_start_time = None
        self.current_query_text = None
        
        # Validate environment and initialize components
        self._validate_environment()
        
        # The knowledge base (Vector DB) is loaded in the background to avoid
        # blocking startup. An event will signal when it's ready.
        self.vector_db_client = None
        self.p2p_functions = None
        self._kb_ready_event = asyncio.Event()
        
        # Create enhanced instructions first before super().__init__

        # All critical behaviors now consolidated in _create_corrected_instructions
        enhanced_instructions = self._create_corrected_instructions()
        
        # Available Gemini voices
        AVAILABLE_VOICES = {
            'male': ['Puck', 'Charon', 'Fenrir', 'Orus', 'Enceladus', 'Iapetus', 'Umbriel'],
            'female': ['Aoede', 'Kore', 'Leda', 'Zephyr', 'Autonoe', 'Callirrhoe', 'Despina', 'Erinome', 'Sadachbia']
        }
        
        # Choose a voice (you can change this to any voice from AVAILABLE_VOICES)
        selected_voice = 'Kore'  # Female voice with natural tone
        
        # Validate selected voice
        all_voices = AVAILABLE_VOICES['male'] + AVAILABLE_VOICES['female']
        if selected_voice not in all_voices:
            logger.warning(f"Invalid voice '{selected_voice}'. Defaulting to 'Kore'")
            selected_voice = 'Callirrhoe'
        
        logger.info(f"Using voice: {selected_voice}")
        
        super().__init__(
            instructions=enhanced_instructions,
            
            # Use Gemini's RealtimeModel with Vertex AI
            #llm=google.beta.realtime.RealtimeModel(
            llm=RealtimeModel(
                # model="gemini-2.5-flash-preview-native-audio-dialog",
                # model="gemini-2.0-flash-exp",
                # model="gemini-2.0-flash-live-001",
                # model = 'gemini-2.5-flash-preview-native-audio-dialog',
                model = 'gemini-live-2.5-flash-preview',
                language="en-IN",
                voice=selected_voice,
                temperature=0.7,
                #instructions=enhanced_instructions,
                #vertexai=True,
                project=os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-project-459106'),
                location='us-central1'
            ),
            chat_ctx=chat_ctx,
            # Enhanced interrupt handling
            allow_interruptions=True
        )
        
        logger.info(" NeuraVoice V2 initialized successfully with sophisticated personality!")

    async def on_enter(self):
        """Called when agent enters the session - provide greeting with robust error handling for LiveKit metrics bug."""
        import random
        
        # Simple working greeting instructions
        greeting_variations = [
            "Mention who you are and greet the user in short sentence",
            "Mention who you are and greet the user and talk about the purpose of the call in short sentence",
            "Introduce yourself briefly as Priya from LendenClub and greet the user in English",
            "Greet the user warmly, mention you're Priya from LendenClub calling about P2P lending"
        ]
        
        # Randomly select one of the greeting variations
        selected_greeting = random.choice(greeting_variations)
        logger.info(" [ON_ENTER] Attempting to generate initial greeting")
        logger.info(f" [ON_ENTER] Selected greeting instruction: {selected_greeting}")
        
        # Enhanced error handling for the known LiveKit metrics bug (GitHub issue #2349)
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.session.generate_reply(instructions=selected_greeting)
                logger.info(" [ON_ENTER] Greeting generation initiated successfully")
                # TIMING OPTIMIZATION: Give audio pipeline time to process
                logger.info(" [ON_ENTER] Waiting for audio processing to stabilize...")
                await asyncio.sleep(5.0)  # Allow audio pipeline to process without metrics conflicts
                
                return  # Success, exit the retry loop
                
            except ZeroDivisionError as e:
                logger.warning(f" [ON_ENTER] LiveKit metrics bug encountered (attempt {attempt + 1}/{max_retries}): {e}")
                logger.warning(" [ON_ENTER] This is a known issue in LiveKit Agents 1.0.21+ (GitHub #2349)")
                if attempt < max_retries - 1:
                    await asyncio.sleep(0.5)  # Brief delay before retry
                    continue
                else:
                    logger.error(" [ON_ENTER] All greeting attempts failed due to metrics bug")
                    
            except TypeError as e:
                if "NoneType" in str(e):
                    logger.warning(f" [ON_ENTER] LiveKit token count bug encountered (attempt {attempt + 1}/{max_retries}): {e}")
                    logger.warning(" [ON_ENTER] This is a known issue in LiveKit Agents 1.0.21+ (GitHub #2349)")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(0.5)  # Brief delay before retry
                        continue
                    else:
                        logger.error(" [ON_ENTER] All greeting attempts failed due to token count bug")
                else:
                    logger.error(f" [ON_ENTER] Unexpected TypeError in greeting generation: {e}")
                    break
                    
            except Exception as e:
                logger.error(f" [ON_ENTER] Unexpected error in greeting generation: {e}")
                # Try fallback greeting once
                try:
                    logger.info(" [ON_ENTER] Trying fallback greeting...")
                    self.session.generate_reply(instructions="Greet the user briefly")
                    logger.info(" [ON_ENTER] Fallback greeting initiated")
                    return
                except Exception as e2:
                    logger.error(f" [ON_ENTER] Fallback greeting also failed: {e2}")
                    break
        
        logger.info(" [ON_ENTER] Agent ready for user input (greeting generation failed due to LiveKit bugs)")
        logger.info(" [ON_ENTER] Tip: Consider downgrading to LiveKit Agents 1.0.20 to avoid these issues")


    async def on_user_speech_committed(self, user_speech: str):
        """Called when user speech is committed - start metrics tracking."""
        logger.info(f" [USER SPEECH COMMITTED] Starting metrics for: '{user_speech[:50]}...'")
        self.start_query_metrics(user_speech)
        
        # Call parent method if it exists
        if hasattr(super(), 'on_user_speech_committed'):
            await super().on_user_speech_committed(user_speech)

    async def on_agent_speech_committed(self, agent_speech: str):
        """Called when agent speech is committed - end metrics tracking and update conversation memory."""
        logger.info(f" [AGENT SPEECH COMMITTED] Ending metrics for: '{agent_speech[:50]}...'")
        self.end_query_metrics(agent_speech)
        
        # Call parent method if it exists
        if hasattr(super(), 'on_agent_speech_committed'):
            await super().on_agent_speech_committed(agent_speech)

    def log_startup_status(self):
        """Log comprehensive startup status."""
        logger.info("="*80)
        logger.info(" [NEURAVOICE V2 STARTUP] System initialization complete!")
        logger.info("="*80)
        logger.info(" [PERSONALITY] Dynamic personality traits active")
        logger.info(" [MICRO-INTERACTIONS] Sophisticated micro-interactions enabled")
        logger.info(" [EMOTION DETECTION] Advanced emotion detection with intensity tracking")
        logger.info(" [NOISE FILTERING] Enhanced noise filtering active")
        logger.info(" [VECTOR DB] Knowledge base loaded and tested")
        logger.info(" [LLM] Using Gemini RealtimeModel with enhanced instructions")
        logger.info(" [LANGUAGE] Multi-language support: English, Hindi, Hinglish")
        logger.info("="*80)
        
        # Display performance metrics
        stats = self.get_performance_stats()
        logger.info(f" [METRICS] Queries: {stats['total_queries']} | Functions: {stats['function_calls']} | Vector searches: {stats['vector_searches']}")
        
        # Display personality traits
        if hasattr(self, 'personality') and self.personality:
            traits = self.personality.traits
            logger.info(f" [PERSONALITY TRAITS] Wit: {traits.get('wit_level', 0):.1f}/1.0 | Curiosity: {traits.get('curiosity', 0):.1f}/1.0 | Warmth: {traits.get('warmth', 0):.1f}/1.0")
            
        logger.info("="*80)
        logger.info(" [SYSTEM READY] NeuraVoice V2 is FULLY OPERATIONAL and ready for sophisticated conversations!")
        logger.info("="*80)

    def _validate_environment(self):
        """Validate required environment variables."""
        required_vars = {
            'GOOGLE_APPLICATION_CREDENTIALS': os.getenv('GOOGLE_APPLICATION_CREDENTIALS'),
            'GOOGLE_CLOUD_PROJECT': os.getenv('GOOGLE_CLOUD_PROJECT')
        }
        
        # Set default project if not provided
        if not required_vars['GOOGLE_CLOUD_PROJECT']:
            os.environ['GOOGLE_CLOUD_PROJECT'] = 'ai-project-459106'
            required_vars['GOOGLE_CLOUD_PROJECT'] = 'ai-project-459106'
        
        missing_vars = [var for var, value in required_vars.items() if not value]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        logger.info(" Environment variables validated")
    
    def _create_corrected_instructions(self) -> str:
        """Create instructions for the LLM using the single enhanced system prompt."""
        return get_enhanced_system_prompt()
    
    def _test_vector_db(self):
        """Test vector database connectivity and functionality with comprehensive logging."""
        try:
            logger.info("="*80)
            logger.info("🧪 [VECTOR DB TEST STARTED] Testing vector database connectivity...")
            logger.info("="*80)
            
            start_time = time.time()
            
            # Test with a simple P2P query
            test_query = "What is P2P lending?"
            logger.info(f"🧪 [VECTOR DB TEST] Running test query: '{test_query}'")
            
            test_results = self.p2p_functions.search_p2p_knowledge(test_query, max_results=2)
            
            end_time = time.time()
            search_duration = end_time - start_time
            
            logger.info(f"🧪 [VECTOR DB TEST] Search completed in {search_duration:.3f} seconds")
            logger.info(f"🧪 [VECTOR DB TEST] Results count: {len(test_results)}")
            
            if test_results:
                logger.info("📋 [VECTOR DB TEST] Sample results found:")
                for i, result in enumerate(test_results):
                    content_preview = result.get('content', '')[:150] + "..." if len(result.get('content', '')) > 150 else result.get('content', '')
                    source_info = result.get('metadata', {}).get('source', 'Unknown')
                    logger.info(f"   📄 Result {i+1} (Source: {source_info}): {content_preview}")
                
                logger.info("="*80)
                logger.info("✅ [VECTOR DB TEST SUCCESS] Vector database is FULLY OPERATIONAL!")
                logger.info(f"✅ [VECTOR DB TEST SUCCESS] Ready to serve P2P lending queries with {search_duration:.3f}s avg response time")
                logger.info("="*80)
            else:
                logger.warning("="*80)
                logger.warning("⚠️ [VECTOR DB TEST WARNING] Vector database returned no results!")
                logger.warning("⚠️ [VECTOR DB TEST WARNING] Check if knowledge base is properly loaded")
                logger.warning("="*80)
                
        except Exception as e:
            logger.error("="*80)
            logger.error(f"❌ [VECTOR DB TEST FAILED] Vector database test failed: {str(e)}")
            logger.error(f"❌ [VECTOR DB TEST FAILED] This will impact P2P question answering capability")
            logger.error(f"❌ [VECTOR DB TEST FAILED] Traceback: {traceback.format_exc()}")
            logger.error("="*80)

    
    @function_tool
    async def search_knowledge_base(self, context: RunContext, user_query: str, max_results: int = 3):
        """Called when user asks about P2P lending, Lenden Club, finance topics, RBI regulations, 
        competitors, investments, company details, or any personal financial queries.
    
        Args:
            user_query: The user's P2P lending or finance-related question to search for
            max_results: Maximum number of results to return (default: 3)
        """
        logger.info("="*80)
        
        
        
                
        logger.info("="*60)
        logger.info(f"🔍 Searching KB for: '{user_query[:50]}...'")
        
        # Wait for the knowledge base to be ready before proceeding
        await self._kb_ready_event.wait()
        
        if self.p2p_functions is None:
            logger.error("❌ KB not available, search cannot proceed.")
            return [{"content": "I'm currently unable to access my knowledge base. Please try again shortly.", "source": "error"}]
        
        self.performance_metrics['function_calls'] += 1
        self.performance_metrics['vector_searches'] += 1
        
        try:
            start_time = time.time()
            results = self.p2p_functions.search_p2p_knowledge(user_query, max_results)
            search_duration = time.time() - start_time
            
            logger.info(f"✅ KB search completed: {len(results)} results in {search_duration:.2f}s")
            
            if not results:
                return [{"content": "I don't have specific information about that. Could you rephrase your question?", "source": "fallback"}]
            
            # Simplified result formatting
            formatted_results = []
            for i, result in enumerate(results, 1):
                formatted_results.append({
                    "content": result.get('content', ''),
                    "source": result.get('metadata', {}).get('source', 'Knowledge Base'),
                    "result_number": i
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"❌ KB search error: {e}")
            return [{"content": f"Search error: {str(e)}", "source": "error"}]
    
    @function_tool
    async def web_search(self, context: RunContext,query: str):
        """
        Use this function to search the web for real-time information, such as latest news, weather forecasts,
        current events, or any topic that requires up-to-date knowledge beyond the internal knowledge base.
        Call this when the user explicitly asks to "search the web", "google something", or asks about
        topics like today's weather, recent news, or stock prices.

        Args:
            query: The user's question to search on the web.
        
        Returns:
            A string containing the answer found from the web search, or a message if no answer is found.
        """
        logger.info(f"🌐 [WEB SEARCH] Performing web search for: '{query}'")
        
        # --- ADDED FOR BACKGROUND AUDIO ---
        # This delay simulates a long web search. While the agent is "await"ing here,
        # the AgentSession enters the "thinking" state, which will automatically
        # trigger the "thinking_sound" configured in the BackgroundAudioPlayer.
        
        
        logger.info("Agent is now 'thinking' during the web search... Keyboard sounds will play.")
        await asyncio.sleep(5)
        # --- END OF ADDED CODE ---

        try:
            # Per user instruction, using the provided API key.
            api_key = "60dd18d0-2fa7-4e00-9a34-b14fb579c165"
            client = LinkupClient(api_key=api_key)

            def do_search():
                # This is a synchronous call
                return client.search(
                    query=query,
                    depth="standard",
                    output_type="sourcedAnswer",
                )

            loop = asyncio.get_running_loop()
            # Run the synchronous search in an executor to avoid blocking the async event loop
            response = await loop.run_in_executor(None, do_search)

            # The response object has an 'answer' attribute. We return this to the LLM.
            if hasattr(response, 'answer') and response.answer:
                logger.info(f"✅ [WEB SEARCH] Success. Answer: {response.answer}")
                return response.answer
            else:
                logger.warning(f"⚠️ [WEB SEARCH] Web search for '{query}' returned no answer.")
                return "I couldn't find a direct answer for that. Please try rephrasing your question."

        except Exception as e:
            logger.error(f"❌ [WEB SEARCH] Error during web search for '{query}': {e}")
            logger.error(traceback.format_exc())
            return "I encountered an error while trying to search the web. Please try again later."

    @function_tool
    async def transfer_to_feedback_agent(self, reason: str):
        """
        Call this function to end the current conversation and transfer the user to a feedback agent.
        Use this when the user's main queries have been resolved and they are ready to end the call,
        or if they say something like 'goodbye', 'thank you, that's all', or 'I'm done'.

        Args:
            reason: A brief explanation of why the transfer is being initiated.
        """
        logger.info(f" [HANDOFF] Transferring to feedback agent. Reason: {reason}")
        # The session's chat context is passed to the new agent to maintain conversation history
        return FeedbackCollectorAgent(chat_ctx=self.chat_ctx)

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        return {
            'total_queries': self.performance_metrics['total_queries'],
            'function_calls': self.performance_metrics['function_calls'],
            'vector_searches': self.performance_metrics['vector_searches'],
            'avg_response_time': self.performance_metrics['avg_response_time']
        }

    def start_query_metrics(self, user_input: str):
        """Start tracking metrics for a new user query."""
        self.current_query_start_time = time.time()
        self.current_query_text = user_input
        logger.info(f"📊 [QUERY METRICS START] Query: '{user_input[:50]}...' | Start time: {self.current_query_start_time}")
        self.performance_metrics['total_queries'] += 1

    def end_query_metrics(self, assistant_response: str):
        """End tracking metrics for the current query and log comprehensive stats."""
        if self.current_query_start_time is None:
            return
        
        end_time = time.time()
        total_time = end_time - self.current_query_start_time
        
        # Get latest usage summary
        usage_summary = self.usage_collector.get_summary()
        
        # Create comprehensive query metrics
        query_metric = {
            'timestamp': datetime.now().isoformat(),
            'user_query': self.current_query_text,
            'assistant_response': assistant_response,
            'total_time_seconds': total_time,
            'usage_summary': usage_summary,
            'query_number': len(self.query_metrics) + 1
        }
        
        self.query_metrics.append(query_metric)
        
        # Trim stored metrics to last 100 entries to prevent unbounded memory growth
        if len(self.query_metrics) > 100:
            self.query_metrics = self.query_metrics[-100:]
        
        # 📊 COMPREHENSIVE METRICS LOGGING
        logger.info("="*120)
        logger.info(f"📊 [QUERY METRICS COMPLETE] Query #{query_metric['query_number']}")
        logger.info("="*120)
        logger.info(f"👤 [USER INPUT]: {self.current_query_text}")
        logger.info(f"🤖 [ASSISTANT RESPONSE]: {assistant_response}")
        logger.info(f"⏱️ [TOTAL TIME]: {total_time:.3f} seconds")
        
        # Log detailed usage metrics
        if usage_summary:
            logger.info("📈 [USAGE METRICS]:")
            for service, metrics in usage_summary.items():
                logger.info(f"  🔧 {service.upper()}:")
                if isinstance(metrics, dict):
                    for metric_name, value in metrics.items():
                        if isinstance(value, (int, float)):
                            logger.info(f"    • {metric_name}: {value}")
                        else:
                            logger.info(f"    • {metric_name}: {value}")
                else:
                    logger.info(f"    • Total: {metrics}")
        
        # Log TTS text that will be spoken
        logger.info("="*120)
        logger.info("🔊 [TTS OUTPUT] Text that will be spoken:")
        logger.info(f"🗣️ \"{assistant_response}\"")
        logger.info("="*120)
        
        # Reset for next query
        self.current_query_start_time = None
        self.current_query_text = None

        # Update running average response time
        queries_so_far = self.performance_metrics['total_queries']
        if queries_so_far:
            prev_avg = self.performance_metrics['avg_response_time']
            self.performance_metrics['avg_response_time'] = ((prev_avg * (queries_so_far - 1)) + total_time) / queries_so_far

        # Proactive memory housekeeping after each completed query
        self._maybe_cleanup_memory()

    def on_metrics_collected(self, event: MetricsCollectedEvent):
        """Handle metrics collection events (like neuravoice_v2.py)."""
        # Log individual metrics as they come in
        logger.info(f"📊 [METRICS COLLECTED] {event.metrics}")
        
        # Collect for summary
        self.usage_collector.collect(event.metrics)
        
        # Extract key metrics for detailed logging
        for metric in event.metrics:
            metric_type = type(metric).__name__
            
            if hasattr(metric, 'ttfb') and metric.ttfb is not None:
                logger.info(f"⚡ [TTFB] {metric_type}: {metric.ttfb:.3f}s (Time to First Byte)")
            
            if hasattr(metric, 'duration') and metric.duration is not None:
                logger.info(f"⏱️ [DURATION] {metric_type}: {metric.duration:.3f}s")
            
            if hasattr(metric, 'characters_count') and metric.characters_count is not None:
                logger.info(f"📝 [CHARACTERS] {metric_type}: {metric.characters_count}")
            
            if hasattr(metric, 'tokens_count') and metric.tokens_count is not None:
                logger.info(f"🔢 [TOKENS] {metric_type}: {metric.tokens_count}")
            
            if hasattr(metric, 'audio_duration') and metric.audio_duration is not None:
                logger.info(f"🎵 [AUDIO DURATION] {metric_type}: {metric.audio_duration:.3f}s")

    def log_final_usage_summary(self):
        """Log final usage summary (called on shutdown)."""
        summary = self.usage_collector.get_summary()
        logger.info("="*120)
        logger.info("📊 [FINAL USAGE SUMMARY]")
        logger.info("="*120)
        logger.info(f"📈 Total Queries Processed: {len(self.query_metrics)}")
        
        if summary:
            for service, metrics in summary.items():
                logger.info(f"🔧 {service.upper()} USAGE:")
                if isinstance(metrics, dict):
                    for metric_name, value in metrics.items():
                        logger.info(f"  • {metric_name}: {value}")
                else:
                    logger.info(f"  • Total: {metrics}")
        
        # Log average response time
        if self.query_metrics:
            avg_time = sum(q['total_time_seconds'] for q in self.query_metrics) / len(self.query_metrics)
            logger.info(f"⏱️ Average Response Time: {avg_time:.3f}s")
        
        logger.info("="*120)
        
    async def _initialize_kb(self):
        """
        Initializes the knowledge base in the background.
        This is a potentially long-running operation that loads models into memory.
        """
        logger.info("📚 Starting background initialization of knowledge base...")

        def _blocking_load():
            """Synchronous loading logic to be run in an executor."""
            logger.info("... loading VectorDBClient and P2PFunctions ...")
            vector_db_client = VectorDBClient(
                db_path=VECTOR_DB_PATH,
                config_path=VECTOR_CONFIG_PATH
            )
            p2p_functions = P2PLendingFunctions(vector_db_client)
            logger.info("... completed loading VectorDBClient and P2PFunctions.")
            return vector_db_client, p2p_functions

        try:
            loop = asyncio.get_running_loop()
            self.vector_db_client, self.p2p_functions = await loop.run_in_executor(
                None, _blocking_load
            )
            self._kb_ready_event.set()
            logger.info("✅ Knowledge base initialized successfully in the background.")
        except Exception as e:
            logger.error(f"❌ Failed to initialize knowledge base in background: {e}")
            logger.error(traceback.format_exc())
            # Set the event anyway to unblock searches, which will then fail gracefully.
            self._kb_ready_event.set()
            
    # Dictionary to store pending responses for portfolio analysis
    _portfolio_analysis_tasks = {}
    
    # ------------------------------------------------------------------
    #                       MEMORY & RESOURCE UTILS
    # ------------------------------------------------------------------
    def _get_process_memory_mb(self) -> float:
        """Return current process RSS memory in MB (best-effort if psutil missing)."""
        if psutil:
            return psutil.Process(os.getpid()).memory_info().rss / (1024 * 1024)
        else:
            # Fallback – not as accurate but avoids hard dependency
            import resource, sys
            rusage_denom = 1024 if sys.platform == 'darwin' else 1024 * 1024
            return resource.getrusage(resource.RUSAGE_SELF).ru_maxrss / rusage_denom

    def _maybe_cleanup_memory(self, force: bool = False):
        """Attempt to free memory if usage crosses threshold or when forced."""
        mem_mb = self._get_process_memory_mb()
        threshold = MEMORY_WARN_MB if not force else MEMORY_CRITICAL_MB
        if mem_mb >= threshold:
            logger.warning(f"[MEMORY] High usage detected: {mem_mb:.0f} MB. Initiating cleanup…")
            # 1) Clear vector-DB cache if available
            try:
                if self.vector_db_client:
                    self.vector_db_client.clear_cache()
            except Exception as e:
                logger.debug(f"[MEMORY] Could not clear vector DB cache: {e}")

            # 2) Explicit garbage collection
            collected = gc.collect()
            logger.info(f"[MEMORY] Garbage collector reclaimed {collected} objects")

            # 3) Log post-cleanup usage
            mem_after = self._get_process_memory_mb()
            logger.info(f"[MEMORY] Usage after cleanup: {mem_after:.0f} MB")


class FeedbackCollectorAgent(Agent):
    """An agent that asks the user for feedback about their experience."""
    def __init__(self, chat_ctx: ChatContext):
        super().__init__(
            instructions = """
                    Greet the user warmly as the conversation ends.  
                    Ask how their experience was speaking with you today.  
                    Respond naturally to their feedback, thank them sincerely, and end the call gracefully.
            """,
            llm=RealtimeModel(
                model='gemini-live-2.5-flash-preview',
                language="en-IN",
                voice="Kore",
                temperature=0.7,
                project=os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-project-459106'),
                location='us-central1'
            ),
            chat_ctx=chat_ctx,
            allow_interruptions=True
        )

    @function_tool
    async def transfer_back_to_main_agent(self, context: RunContext, reason: str):
        """
        Call this function if the user indicates they want to continue the conversation,
        ask more questions, or talk about something else instead of providing feedback.
        Use this to transfer them back to the main agent.

        Args:
            reason: A brief explanation of why the transfer is being initiated (e.g., "User wants to ask another question.").
        """
        logger.info(f" [HANDOFF] Transferring back to main agent. Reason: {reason}")
        # The session's chat context is passed to the new agent to maintain conversation history
        return NeuraVoiceV2(chat_ctx=self.chat_ctx)

    async def on_enter(self):
        """Called when the agent enters the session. Proactively asks for feedback."""
        logger.info(" [FEEDBACK AGENT] Entering session to collect feedback.")
        # Proactively ask for feedback instead of waiting for user speech.
        self.session.generate_reply(instructions="Ask the user for their feedback on the experience with the previous agent in a single, brief sentence.")


async def entrypoint(ctx: JobContext):
    """FIXED: Following lendbot.py pattern - proper session configuration like working code."""
    logger.info("🚀 Starting NeuraVoice V2 following lendbot.py working pattern...")
    
    try:
        # Connect to LiveKit server
        await ctx.connect()
        logger.info("✅ Connected to LiveKit server")
        selected_voice = 'Kore'

        # -------------------------------------------------------------------- #
        # ------ 🚀 START: USER INACTIVITY CHECKOUT IMPLEMENTATION 🚀 ------ #
        # -------------------------------------------------------------------- #
        
        # 1. Configuration Constants (easy to tune)
        USER_AWAY_TIMEOUT_S = 15.0  # Seconds of silence before we consider the user 'away'
        USER_CHECKOUT_INTERVAL_S = 10.0 # Seconds to wait for a response after each prompt
        USER_CHECKOUT_ATTEMPTS = 2      # Number of times to check before ending the call

        # This will hold the background task that checks for user presence
        inactivity_task: asyncio.Task | None = None

        # 2. The Checkout Logic: Defines what happens when the user is inactive
        async def user_inactivity_task(session: AgentSession):
            """
            This task runs in the background when the user is inactive.
            It prompts the user to check if they're still there and hangs up if not.
            """
            logger.info(f"User has been inactive for {USER_AWAY_TIMEOUT_S}s. Starting checkout procedure...")
            for i in range(USER_CHECKOUT_ATTEMPTS):
                logger.info(f"Performing inactivity check #{i+1}/{USER_CHECKOUT_ATTEMPTS}...")
                await session.generate_reply(
                    instructions="The user has been silent for a while. Politely check if they are still present.Do not read the prompt  '"
                )
                # Wait for the configured interval to give the user a chance to respond
                await asyncio.sleep(USER_CHECKOUT_INTERVAL_S)

            # If the loop completes without being cancelled, the user is gone.
            logger.warning("User did not respond to inactivity checks. Ending the session.")
            await asyncio.shield(session.aclose()) # Gracefully close the agent's connection
            ctx.delete_room() # Terminate the entire room on the server

        # 3. The Event Handler (The "Glue"): Connects the trigger to the action
        # This will be attached to the session after it's created.
        def _setup_inactivity_handler(session: AgentSession):
            @session.on("user_state_changed")
            def _on_user_state_changed(ev: UserStateChangedEvent):
                nonlocal inactivity_task
                
                # If the user becomes 'away', start our checkout task
                if ev.new_state == "away":
                    if inactivity_task is None or inactivity_task.done():
                        logger.info("User state changed to 'away'. Creating inactivity task.")
                        inactivity_task = asyncio.create_task(user_inactivity_task(session))
                
                # If the user becomes active again (e.g., starts listening/speaking),
                # we must cancel the checkout task.
                else:
                    if inactivity_task is not None and not inactivity_task.done():
                        logger.info("User activity detected. Cancelling inactivity checkout task.")
                        inactivity_task.cancel()
                        inactivity_task = None
        
        # -------------------------------------------------------------------- #
        # ------- 🚀 END: USER INACTIVITY CHECKOUT IMPLEMENTATION 🚀 ------- #
        # -------------------------------------------------------------------- #


        # Create NeuraVoice V2 bot instance 
        bot = NeuraVoiceV2(chat_ctx=ChatContext()) # ctx
        logger.info("✅ NeuraVoice V2 created")

        # Start loading the knowledge base in a background task
        asyncio.create_task(bot._initialize_kb())
        logger.info("🚀 Knowledge base initialization started in the background.")

        # Create AgentSession with VAD and timeout for inactivity detection
        session = AgentSession(
            vad=silero.VAD.load(),
            user_away_timeout=USER_AWAY_TIMEOUT_S,
        )
        logger.info(f"✅ Created AgentSession with Silero VAD and user_away_timeout of {USER_AWAY_TIMEOUT_S}s.")

        # Attach the inactivity handler to the session
        _setup_inactivity_handler(session)
        logger.info("✅ Inactivity checkout handler configured.")


        # --- ADDED FOR BACKGROUND AUDIO ---
        logger.info("🎧 Configuring background audio for a more realistic experience...")
        background_audio = BackgroundAudioPlayer(
            # Play office ambience sound looping in the background
            ambient_sound=AudioConfig(BuiltinAudioClip.OFFICE_AMBIENCE, volume=0.8),
            # Play keyboard typing sound when the agent is "thinking" (e.g., during web search)
            thinking_sound=[
                AudioConfig(BuiltinAudioClip.KEYBOARD_TYPING, volume=0.8),
                AudioConfig(BuiltinAudioClip.KEYBOARD_TYPING2, volume=0.7),
            ],
        )
        # The audio player needs to be started so it can publish its track and listen for events.
        # We do this *before* starting the main agent session loop.
        await background_audio.start(room=ctx.room, agent_session=session)
        logger.info("✅ Background audio player started.")
        # --- END OF ADDED CODE ---

        # 📊 SET UP COMPREHENSIVE METRICS COLLECTION (like neuravoice_v2.py)
        @session.on("metrics_collected")
        def _on_metrics_collected(event: MetricsCollectedEvent):
            """Handle metrics collection events - log and collect usage data."""
            # Log metrics as they are emitted (like neuravoice_v2.py)
            metrics.log_metrics(event.metrics)
            
            # Pass to bot for detailed processing
            bot.on_metrics_collected(event)

        # 📊 SET UP SHUTDOWN CALLBACK FOR FINAL USAGE SUMMARY (like neuravoice_v2.py)
        async def log_final_usage():
            """Log final usage summary and cleanup resources on shutdown."""
            bot.log_final_usage_summary()
            # 💾 Save persistent cache to disk
            if getattr(bot, 'vector_db_client', None):
                bot.vector_db_client.shutdown()
                logger.info("💾 Vector DB cache saved to disk")

        ctx.add_shutdown_callback(log_final_usage)
        logger.info("✅ Metrics collection and shutdown callbacks configured")
    
        # Log startup status
        bot.log_startup_status()

        # Start the session with the agent and proper room options
        await session.start(
            agent=bot,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                noise_cancellation=noise_cancellation.BVC()
            ),
            room_output_options=RoomOutputOptions(
                audio_enabled=True
            )
        )

        logger.info("✅ AgentSession started with NeuraVoice V2 bot and comprehensive metrics collection")
        
    except Exception as e:
        logger.exception(f"❌ Error in entrypoint: {str(e)}")
        raise

if __name__ == "__main__":
    # Increase LiveKit memory warning threshold to avoid spammy logs when the voice
    # agent legitimately needs >2 GB RAM (RealtimeModel + other models).
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            job_memory_warn_mb=max(MEMORY_WARN_MB, 2500),  # raise from default 300 MB
            job_memory_limit_mb=0,  # keep unlimited but warn at higher threshold
        )
    )