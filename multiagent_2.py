import os
import sys
import asyncio
import logging
import traceback
import re
import time
import json
import random
import requests
import gc  # For manual garbage collection
# psutil is optional – only used if available for accurate memory usage reporting
try:
    import psutil
except ImportError:
    psutil = None
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from contextlib import nullcontext
from livekit import rtc
from livekit.agents import cli, WorkerOptions, Agent, JobContext, AgentSession, metrics
from livekit.agents.voice.room_io import RoomInputOptions, RoomOutputOptions
from livekit.agents.llm import ChatContext, ChatMessage, function_tool
from livekit.agents.voice import MetricsCollectedEvent
from livekit.agents.voice import Agent, AgentSession, RunContext
from livekit.plugins import (
    silero,
    google,
    noise_cancellation,
)
from clients.vector_db_client import VectorDBClient
from prompts import get_enhanced_system_prompt
from utils.performance_monitor import PerformanceMonitor, PerformanceContext
from livekit.plugins.google.beta.realtime.realtime_api import RealtimeModel

import logging
from dataclasses import dataclass
from typing import Optional
import os

from dotenv import load_dotenv

from livekit import api
from livekit.agents import (
    Agent,
    AgentSession,
    ChatContext,
    JobContext,
    JobProcess,
    RoomInputOptions,
    RoomOutputOptions,
    RunContext,
    WorkerOptions,
    cli,
    metrics,
)
from livekit.agents.job import get_job_context
from livekit.agents.llm import function_tool
from livekit.agents.voice import MetricsCollectedEvent
from livekit.plugins import silero
from livekit.plugins.google.beta.realtime.realtime_api import RealtimeModel


# Configure logging - let LiveKit CLI handle logging configuration
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 🔇 SUPPRESS VERBOSE DEBUG LOGS from external libraries
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)
logging.getLogger('requests').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('httpcore').setLevel(logging.WARNING)
logging.getLogger('transformers').setLevel(logging.WARNING)
logging.getLogger('huggingface_hub').setLevel(logging.WARNING)
logging.getLogger('sentence_transformers').setLevel(logging.WARNING)
logging.getLogger('torch').setLevel(logging.WARNING)
logging.getLogger('chromadb').setLevel(logging.WARNING)
logging.getLogger('openai').setLevel(logging.WARNING)
logging.getLogger('groq').setLevel(logging.WARNING)

# Keep important logs but reduce noise
logging.getLogger('livekit').setLevel(logging.INFO)
logging.getLogger('google').setLevel(logging.INFO)

# Load environment variables
load_dotenv()

# LiveKit components are already imported above
try:
    logger.info("✅ LiveKit components imported successfully")
except ImportError as e:
    logger.error(f"❌ Failed to import LiveKit components: {str(e)}")
    sys.exit(1)


from clients.vector_db_client import VectorDBClient

# Constants
BASE_DIR = Path(__file__).resolve().parent
VECTOR_DB_PATH = BASE_DIR / "vectordb" / "bge-m3"
VECTOR_CONFIG_PATH = BASE_DIR / "vectordb" / "vectordb_config.yml"

MEMORY_WARN_MB = int(os.getenv("NEURAVOICE_MEMORY_WARN_MB", "1500"))  # soft cleanup threshold
MEMORY_CRITICAL_MB = int(os.getenv("NEURAVOICE_MEMORY_CRITICAL_MB", "2500"))  # aggressive cleanup threshold

    
class NeuraVoiceV2(Agent):
    """
    FIXED: NeuraVoice V2 inheriting from Agent like witty_voice_bot.py
    This ensures proper integration of instructions and Gemini LLM together.
    """
    
    
    def __init__(self): #  ctx: JobContext
        #self.ctx = ctx
        # Validate environment and initialize components
        self._validate_environment()
        self.vector_db_client = None  # Lazy init
        
        # Create enhanced instructions first before super().__init__

        # All critical behaviors now consolidated in _create_corrected_instructions
        enhanced_instructions = self._create_corrected_instructions()
        
        # Available Gemini voices
        llm=RealtimeModel(
                model="gemini-live-2.5-flash-preview",
                # model = 'gemini-2.5-flash-preview-native-audio-dialog',
                language="en-US",
                voice="Zephyr",
                temperature=0.7,
                project=os.getenv("GOOGLE_CLOUD_PROJECT", "ai-project-459106"),
                location="us-central1",
            )
        
    async def on_enter(self):
        # when the agent is added to the session, we'll initiate the conversation by
        # using the LLM to generate a reply
        self.session.generate_reply()
    
    @function_tool
    async def search_knowledge_base(self, context: RunContext, user_query: str, max_results: int = 3):
        """Search P2P lending knowledge base - optimized for speed.
        
        Args:
            context: RunContext from LiveKit
            user_query: The user's P2P lending question to search for
            max_results: Maximum number of results to return (default: 3)
        """
        logger.info("="*80)
        logger.info(context)
        print(context)
        # chat_history = context.chat_ctx
        # if not chat_history.items:
        #     logger.info("  (No prior history in this session)")
        # else:
        #     for msg in chat_history.items:
        #         # Sanitize content for logging to avoid overly long lines
        #         content_preview = str(msg.content).replace('\n', ' ').strip()
        #         if len(content_preview) > 100:
        #             content_preview = content_preview[:97] + "..."
        #         logger.info(f"  - [{msg.role.upper()}]: {content_preview}")
                
        
        try:
            start_time = time.time()
            results = self.p2p_functions.search_p2p_knowledge(user_query, max_results)
            search_duration = time.time() - start_time
            
            logger.info(f"✅ KB search completed: {len(results)} results in {search_duration:.2f}s")
            
            if not results:
                return [{"content": "I don't have specific information about that. Could you rephrase your question?", "source": "fallback"}]
            
            # Simplified result formatting
            formatted_results = []
            for i, result in enumerate(results, 1):
                formatted_results.append({
                    "content": result.get('content', ''),
                    "source": result.get('metadata', {}).get('source', 'Knowledge Base'),
                    "result_number": i
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"❌ KB search error: {e}")
            return [{"content": f"Search error: {str(e)}", "source": "error"}]
        v2 = VoiceAgent()
        
def prewarm(proc: JobProcess):
    proc.userdata["vad"] = silero.VAD.load()
    

def _validate_environment():
    """Ensure required Google Cloud credentials are available."""

    required_vars = {
        "GOOGLE_APPLICATION_CREDENTIALS": os.getenv("GOOGLE_APPLICATION_CREDENTIALS"),
        "GOOGLE_CLOUD_PROJECT": os.getenv("GOOGLE_CLOUD_PROJECT"),
    }

    # Set sensible default for project if not provided (matches final_test_noload.py)
    if not required_vars["GOOGLE_CLOUD_PROJECT"]:
        os.environ["GOOGLE_CLOUD_PROJECT"] = "ai-project-459106"
        required_vars["GOOGLE_CLOUD_PROJECT"] = "ai-project-459106"

    missing = [name for name, val in required_vars.items() if not val]
    if missing:
        raise ValueError(
            f"Missing required environment variables for Google RealtimeModel: {', '.join(missing)}"
        )
        
        
class VoiceAgent(Agent):
    def __init__(self):
        super().__init__()
        
    async def on_enter(self):
        self.session.generate_reply()
        

async def entrypoint(ctx: JobContext):
    await ctx.connect()

    # Ensure environment credentials are present before creating the session
    _validate_environment()

    # Use a single Google RealtimeModel instance that handles STT, LLM, and TTS
    session = AgentSession(
        vad=ctx.proc.userdata["vad"],
        llm=RealtimeModel(
            model="gemini-live-2.5-flash-preview",
            language="en-IN",
            voice="Kore",
            temperature=0.7,
            project=os.getenv("GOOGLE_CLOUD_PROJECT", "ai-project-459106"),
            location="us-central1",
        )
    )

    # log metrics as they are emitted, and total usage after session is over
    usage_collector = metrics.UsageCollector()

    @session.on("metrics_collected")
    def _on_metrics_collected(ev: MetricsCollectedEvent):
        metrics.log_metrics(ev.metrics)
        usage_collector.collect(ev.metrics)

    async def log_usage():
        summary = usage_collector.get_summary()
        logger.info(f"Usage: {summary}")

    ctx.add_shutdown_callback(log_usage)

    await session.start(
        agent=NeuraVoiceV2(),
        room=ctx.room,
        room_input_options=RoomInputOptions(
            # uncomment to enable Krisp BVC noise cancellation
            # noise_cancellation=noise_cancellation.BVC(),
        ),
        room_output_options=RoomOutputOptions(transcription_enabled=True),
    )


if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint, prewarm_fnc=prewarm))
    
   

