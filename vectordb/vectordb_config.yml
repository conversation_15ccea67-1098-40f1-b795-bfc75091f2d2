embedding_model: "BAAI/bge-m3"
# Local model directory to avoid repeated downloads
local_model_dir: "models/bge-m3"

# Cache configuration - OPTIMIZED FOR PERFORMANCE
cache:
  size: 5000  # Maximum number of queries to cache (increased from 1000)
  enabled: true
  clear_after: 5000  # Clear entire cache after this many queries (increased from 1000)
  # Persistent cache settings
  persist_cache: true
  cache_file: "vectordb/query_cache.pkl"  # File to persist cache across sessions

# Retriever configuration
retriever_defaults:
  similarity:
    k: 3
  similarity_score_threshold:
    k: 3
    score_threshold: 0.7
  mmr:
    k: 3
    lambda_mult: 0.5