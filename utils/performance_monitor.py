"""
Advanced Performance Monitoring for NeuraVoice v2
This module provides comprehensive performance tracking, latency analysis,
and system health monitoring for the function calling voice agent.
"""

import time
import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
import json
import statistics

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """Individual performance metric data structure."""
    name: str
    value: float
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class FunctionCallMetric:
    """Metrics for individual function calls."""
    function_name: str
    execution_time: float
    success: bool
    timestamp: datetime
    input_size: int = 0
    output_size: int = 0
    error_message: Optional[str] = None

class LatencyTracker:
    """Tracks and analyzes various latency metrics."""
    
    def __init__(self, max_samples: int = 1000):
        self.max_samples = max_samples
        self.latencies = {
            'vector_search': deque(maxlen=max_samples),
            'function_calls': deque(maxlen=max_samples),
            'llm_response': deque(maxlen=max_samples),
            'total_response': deque(maxlen=max_samples),
            'noise_filtering': deque(maxlen=max_samples),
            'language_detection': deque(maxlen=max_samples)
        }
        
    def record_latency(self, operation: str, latency: float):
        """Record latency for a specific operation."""
        if operation in self.latencies:
            self.latencies[operation].append(latency)
        else:
            logger.warning(f"Unknown operation for latency tracking: {operation}")
    
    def get_statistics(self, operation: str) -> Dict[str, float]:
        """Get statistical analysis of latencies for an operation."""
        if operation not in self.latencies or not self.latencies[operation]:
            return {}
        
        data = list(self.latencies[operation])
        return {
            'mean': statistics.mean(data),
            'median': statistics.median(data),
            'min': min(data),
            'max': max(data),
            'std_dev': statistics.stdev(data) if len(data) > 1 else 0,
            'p95': statistics.quantiles(data, n=20)[18] if len(data) >= 20 else max(data),
            'p99': statistics.quantiles(data, n=100)[98] if len(data) >= 100 else max(data),
            'sample_count': len(data)
        }
    
    def get_all_statistics(self) -> Dict[str, Dict[str, float]]:
        """Get statistics for all tracked operations."""
        return {op: self.get_statistics(op) for op in self.latencies.keys()}

class FunctionCallMonitor:
    """Monitors function call performance and success rates."""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.call_history: deque = deque(maxlen=max_history)
        self.function_stats = defaultdict(lambda: {
            'total_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'last_called': None
        })
    
    def record_function_call(self, metric: FunctionCallMetric):
        """Record a function call metric."""
        self.call_history.append(metric)
        
        # Update function-specific stats
        stats = self.function_stats[metric.function_name]
        stats['total_calls'] += 1
        stats['total_time'] += metric.execution_time
        stats['avg_time'] = stats['total_time'] / stats['total_calls']
        stats['last_called'] = metric.timestamp
        
        if metric.success:
            stats['successful_calls'] += 1
        else:
            stats['failed_calls'] += 1
    
    def get_function_stats(self, function_name: str) -> Dict[str, Any]:
        """Get statistics for a specific function."""
        if function_name not in self.function_stats:
            return {}
        
        stats = self.function_stats[function_name].copy()
        stats['success_rate'] = (stats['successful_calls'] / stats['total_calls'] * 100) if stats['total_calls'] > 0 else 0
        stats['failure_rate'] = (stats['failed_calls'] / stats['total_calls'] * 100) if stats['total_calls'] > 0 else 0
        
        return stats
    
    def get_all_function_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all monitored functions."""
        return {func: self.get_function_stats(func) for func in self.function_stats.keys()}
    
    def get_recent_failures(self, minutes: int = 5) -> List[FunctionCallMetric]:
        """Get recent failed function calls."""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [metric for metric in self.call_history 
                if not metric.success and metric.timestamp >= cutoff_time]

class SystemHealthMonitor:
    """Monitors overall system health and performance trends."""
    
    def __init__(self):
        self.health_checks = {}
        self.performance_trends = defaultdict(list)
        self.alerts = []
        
        # Health thresholds
        self.thresholds = {
            'max_response_time': 3.0,  # seconds
            'min_success_rate': 95.0,  # percentage
            'max_error_rate': 5.0,     # percentage
            'max_vector_search_time': 1.0,  # seconds
            'max_function_call_time': 0.5   # seconds
        }
    
    def check_system_health(self, 
                          latency_tracker: LatencyTracker, 
                          function_monitor: FunctionCallMonitor) -> Dict[str, Any]:
        """Perform comprehensive system health check."""
        health_status = {
            'timestamp': datetime.now(),
            'overall_status': 'healthy',
            'issues': [],
            'warnings': []
        }
        
        # Check response time health
        total_response_stats = latency_tracker.get_statistics('total_response')
        if total_response_stats and total_response_stats.get('mean', 0) > self.thresholds['max_response_time']:
            health_status['issues'].append(f"High average response time: {total_response_stats['mean']:.2f}s")
            health_status['overall_status'] = 'degraded'
        
        # Check function success rates
        function_stats = function_monitor.get_all_function_stats()
        for func_name, stats in function_stats.items():
            if stats.get('success_rate', 100) < self.thresholds['min_success_rate']:
                health_status['issues'].append(f"Low success rate for {func_name}: {stats['success_rate']:.1f}%")
                health_status['overall_status'] = 'degraded'
        
        # Check vector search performance
        vector_stats = latency_tracker.get_statistics('vector_search')
        if vector_stats and vector_stats.get('mean', 0) > self.thresholds['max_vector_search_time']:
            health_status['warnings'].append(f"Slow vector search: {vector_stats['mean']:.2f}s average")
        
        # Check recent failures
        recent_failures = function_monitor.get_recent_failures(minutes=5)
        if len(recent_failures) > 5:
            health_status['issues'].append(f"High failure rate: {len(recent_failures)} failures in last 5 minutes")
            health_status['overall_status'] = 'unhealthy'
        
        return health_status
    
    def record_performance_trend(self, metric_name: str, value: float):
        """Record a performance trend metric."""
        self.performance_trends[metric_name].append({
            'value': value,
            'timestamp': datetime.now()
        })
        
        # Keep only last 100 samples
        if len(self.performance_trends[metric_name]) > 100:
            self.performance_trends[metric_name] = self.performance_trends[metric_name][-100:]
    
    def get_performance_trends(self, hours: int = 1) -> Dict[str, List[Dict]]:
        """Get performance trends for the specified time period."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        filtered_trends = {}
        for metric_name, trend_data in self.performance_trends.items():
            filtered_trends[metric_name] = [
                item for item in trend_data 
                if item['timestamp'] >= cutoff_time
            ]
        
        return filtered_trends

class PerformanceMonitor:
    """Main performance monitoring class that coordinates all monitoring components."""
    
    def __init__(self, max_samples: int = 1000):
        self.latency_tracker = LatencyTracker(max_samples)
        self.function_monitor = FunctionCallMonitor(max_samples)
        self.health_monitor = SystemHealthMonitor()
        
        self.start_time = datetime.now()
        self.total_queries = 0
        self.successful_queries = 0
        
        # Performance optimization suggestions
        self.optimization_suggestions = []
    
    async def record_query_start(self) -> str:
        """Record the start of a new query and return a tracking ID."""
        query_id = f"query_{int(time.time() * 1000)}"
        self.total_queries += 1
        return query_id
    
    async def record_query_end(self, query_id: str, success: bool, total_time: float):
        """Record the end of a query."""
        if success:
            self.successful_queries += 1
        
        self.latency_tracker.record_latency('total_response', total_time)
        self.health_monitor.record_performance_trend('response_time', total_time)
    
    def record_operation_latency(self, operation: str, latency: float):
        """Record latency for a specific operation."""
        self.latency_tracker.record_latency(operation, latency)
    
    def record_function_call(self, function_name: str, execution_time: float, 
                           success: bool, error_message: str = None):
        """Record a function call."""
        metric = FunctionCallMetric(
            function_name=function_name,
            execution_time=execution_time,
            success=success,
            timestamp=datetime.now(),
            error_message=error_message
        )
        self.function_monitor.record_function_call(metric)
    
    def get_comprehensive_report(self) -> Dict[str, Any]:
        """Get a comprehensive performance report."""
        uptime = datetime.now() - self.start_time
        
        return {
            'system_info': {
                'uptime_seconds': uptime.total_seconds(),
                'uptime_formatted': str(uptime),
                'total_queries': self.total_queries,
                'successful_queries': self.successful_queries,
                'success_rate': (self.successful_queries / self.total_queries * 100) if self.total_queries > 0 else 0
            },
            'latency_statistics': self.latency_tracker.get_all_statistics(),
            'function_statistics': self.function_monitor.get_all_function_stats(),
            'health_status': self.health_monitor.check_system_health(
                self.latency_tracker, self.function_monitor
            ),
            'performance_trends': self.health_monitor.get_performance_trends(hours=1),
            'optimization_suggestions': self._generate_optimization_suggestions()
        }
    
    def _generate_optimization_suggestions(self) -> List[str]:
        """Generate performance optimization suggestions based on metrics."""
        suggestions = []
        
        # Check vector search performance
        vector_stats = self.latency_tracker.get_statistics('vector_search')
        if vector_stats and vector_stats.get('mean', 0) > 0.8:
            suggestions.append("Consider optimizing vector search with better indexing or caching")
        
        # Check function call performance
        function_stats = self.function_monitor.get_all_function_stats()
        for func_name, stats in function_stats.items():
            if stats.get('avg_time', 0) > 0.5:
                suggestions.append(f"Function {func_name} is slow (avg: {stats['avg_time']:.2f}s) - consider optimization")
            
            if stats.get('success_rate', 100) < 98:
                suggestions.append(f"Function {func_name} has low success rate ({stats['success_rate']:.1f}%) - investigate errors")
        
        # Check overall response time
        total_stats = self.latency_tracker.get_statistics('total_response')
        if total_stats and total_stats.get('p95', 0) > 2.5:
            suggestions.append("95th percentile response time is high - consider performance tuning")
        
        return suggestions
    
    def export_metrics(self, filepath: str):
        """Export metrics to a JSON file for analysis."""
        report = self.get_comprehensive_report()
        
        # Convert datetime objects to strings for JSON serialization
        def datetime_handler(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
        
        try:
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2, default=datetime_handler)
            logger.info(f"Performance metrics exported to {filepath}")
        except Exception as e:
            logger.error(f"Failed to export metrics: {e}")

# Context manager for easy performance tracking
class PerformanceContext:
    """Context manager for tracking operation performance."""
    
    def __init__(self, monitor: PerformanceMonitor, operation_name: str):
        self.monitor = monitor
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            self.monitor.record_operation_latency(self.operation_name, duration)

# Decorator for automatic function performance tracking
def track_performance(monitor: PerformanceMonitor):
    """Decorator to automatically track function performance."""
    def decorator(func: Callable):
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            error_message = None
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error_message = str(e)
                raise
            finally:
                execution_time = time.time() - start_time
                monitor.record_function_call(
                    func.__name__, execution_time, success, error_message
                )
        
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            error_message = None
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error_message = str(e)
                raise
            finally:
                execution_time = time.time() - start_time
                monitor.record_function_call(
                    func.__name__, execution_time, success, error_message
                )
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# Export main classes
__all__ = [
    'PerformanceMonitor',
    'LatencyTracker', 
    'FunctionCallMonitor',
    'SystemHealthMonitor',
    'PerformanceContext',
    'track_performance'
] 