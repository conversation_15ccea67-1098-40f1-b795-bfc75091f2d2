#!/usr/bin/env python3
"""
Runner script for the log parser with different options
"""

import os
import sys
from log_parser import LogParser

def main():
    """Run log parser with different configurations."""
    
    # Initialize parser
    parser = LogParser()
    
    # Check if logs.txt exists
    log_file = "logs.txt"
    if not os.path.exists(log_file):
        print(f"❌ Log file '{log_file}' not found!")
        print("Please make sure 'logs.txt' is in the same directory as this script")
        return
    
    print("🔍 Parsing log file...")
    
    try:
        # Parse the log file
        df = parser.parse_log_file(log_file)
        
        if df.empty:
            print("⚠️ No conversations found in the log file")
            return
        
        # Save as CSV
        csv_file = "conversations.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"✅ Saved {len(df)} conversations to '{csv_file}'")
        
        # Save as Excel if openpyxl is available
        try:
            import openpyxl
            excel_file = "conversations.xlsx"
            df.to_excel(excel_file, index=False)
            print(f"✅ Saved conversations to '{excel_file}'")
        except ImportError:
            print("📝 Install openpyxl to save as Excel: pip install openpyxl")
        
        # Display summary
        print("\n📊 SUMMARY:")
        print(f"   Total conversations: {len(df)}")
        print(f"   Avg user query length: {df['user_query'].str.len().mean():.1f} chars")
        print(f"   Avg assistant response length: {df['assistant_response'].str.len().mean():.1f} chars")
        
        # Show pattern distribution
        if 'pattern_used' in df.columns:
            print("\n🔍 Patterns used:")
            for pattern, count in df['pattern_used'].value_counts().items():
                print(f"   {pattern}: {count} conversations")
        
        # Show sample conversations
        print("\n💬 Sample conversations:")
        for i, row in df.head(2).iterrows():
            print(f"\nConversation {row['conversation_id']}:")
            print(f"   👤 User: {row['user_query'][:80]}{'...' if len(row['user_query']) > 80 else ''}")
            print(f"   🤖 Assistant: {row['assistant_response'][:80]}{'...' if len(row['assistant_response']) > 80 else ''}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 